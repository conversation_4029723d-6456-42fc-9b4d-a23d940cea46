/**
 * ASG Lessons System - Versión Limpia y Refactorizada
 * Sistema de lecciones para AbilitySeminarsGroup
 *
 * Funcionalidades:
 * - Carga de lecciones (video/quiz)
 * - Sistema de progreso con persistencia
 * - Control de acceso y enrollment
 * - Marcado visual de lecciones completadas
 * - Navegación secuencial con bloqueo
 */

// Verificar que estamos en WordPress
if (!defined('ABSPATH')) {
    exit;
}

// Configuración y variables globales
$site_url = get_site_url();
$course_code = isset($_GET['course']) ? sanitize_text_field($_GET['course']) : '';
$lesson_id = isset($_GET['lesson']) ? sanitize_text_field($_GET['lesson']) : '';
$auto_load_first = !$lesson_id && $course_code;


// ===== VERIFICACIÓN ROBUSTA DE ACCESO =====
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$access_denied = false;
$access_message = '';
$redirect_url = '';
$user_status = 'guest'; // guest, logged_in_not_enrolled, enrolled

// Función para verificar enrollment de forma robusta
function asg_check_user_enrollment($user_id, $course_code)
{
    if (!$user_id || !$course_code) {
        return false;
    }

    global $wpdb;

    // Verificar enrollment activo
    $enrollment_check = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}asg_student_enrollments
         WHERE id_user = %d AND code_course = %s AND status = 'active'",
        $user_id,
        $course_code
    ));

    // Log para debugging (remover en producción)
    error_log("ASG Enrollment Check - User: $user_id, Course: $course_code, Result: " . ($enrollment_check ? 'ENROLLED' : 'NOT_ENROLLED'));

    return $enrollment_check !== null;
}

// Verificar estado del usuario y acceso al curso
if (!$user_id) {
    // Usuario no logueado
    $user_status = 'guest';
    $access_denied = true;
    $access_message = 'You need to sign in to access course lessons.';
    $redirect_url = '/wp-login.php?redirect_to=' . urlencode($_SERVER['REQUEST_URI']);
} elseif ($course_code) {
    // Usuario logueado - verificar si es admin primero
    if (current_user_can('manage_options')) {
        // Usuario administrador - acceso total permitido
        $user_status = 'admin';
        $access_denied = false;
        error_log("ASG Access Check - Admin user detected: $user_id - Full access granted");
    } elseif (asg_check_user_enrollment($user_id, $course_code)) {
        // Usuario inscrito - acceso permitido
        $user_status = 'enrolled';
        $access_denied = false;
    } else {
        // Usuario logueado pero no inscrito
        $user_status = 'logged_in_not_enrolled';
        $access_denied = true;
        $access_message = 'You need to purchase this course to access the lessons.';
        $redirect_url = '/online-courses/?course=' . urlencode($course_code);
    }
} else {
    // No hay código de curso
    $access_denied = true;
    $access_message = 'Invalid course access.';
    $redirect_url = '/online-courses/';
}

function asg_render_lessons_page()
{
    global $site_url, $course_code, $lesson_id, $auto_load_first, $access_denied, $access_message;
?>
    <!DOCTYPE html>
    <html lang="es">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="wp-nonce" content="<?php echo wp_create_nonce('wp_rest'); ?>">
        <title>Lecciones - <?php echo esc_html($course_code); ?></title>

        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Bootstrap Icons -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" rel="stylesheet">

        <!-- html2canvas for certificate download -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

        <style>
			
			i.tools{
				font-size: 15px;
				padding: 10px;
			}
            :root {
                --primary-blue: #0C1B41;
                --secondary-blue: #2E5F8A;
                --accent-blue: #4A90E2;
                --accent-yellow: #ffc107;
                --success-color: #28a745;
                --light-gray: #f8f9fa;
                --text-dark: #333;
                --text-light: #666;
                --transition: all 0.3s ease;
            }

            .section-image-left {
                width: 800px;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-weight: bold;
            }

            p {
                font-weight: bold;
            }

            .nav {
                display: none;
            }

            body {
                font-family: 'Outfit', sans-serif;
                background-color: #f5f7fa;
                color: var(--text-dark);
                line-height: 1.6;
            }

            /* Header principal */
            .main-header {
                background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
                color: white;
                padding: 1rem 0;
                position: relative;
                top: 0;
                z-index: 1000;

            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 2rem;
            }

            .course-info {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .back-arrow {
                color: white;
                text-decoration: none;
                font-size: 1.2rem;
                padding: 0.5rem;
                border-radius: 50%;
                transition: var(--transition);
            }

            .back-arrow:hover {
                background: rgba(255, 255, 255, 0.1);
                color: white;
            }

            .course-title {
                font-size: 1.1rem;
                font-weight: 600;
                margin: 0;
            }

            .progress-section {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .progress-container {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .progress-bar {
                width: 200px;
                height: 8px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                overflow: hidden;
            }

            .progress-fill {
                height: 100%;
                background: var(--accent-yellow);
                border-radius: 4px;
                transition: width 0.3s ease;
                width: 0%;
                display: block;
                position: relative;
                z-index: 1;
            }

            .progress-text {
                font-weight: 600;
                font-size: 0.9rem;
            }

            .user-section {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .username {
                font-weight: 500;
            }

            .btn-courses {
                background: var(--accent-yellow);
                color: var(--primary-blue);
                border: none;
                padding: 0.5rem 1.5rem;
                border-radius: 25px;
                font-weight: 600;
                text-decoration: none;
                transition: var(--transition);
            }

            .btn-courses:hover {
                background: #ffb300;
                color: var(--primary-blue);
                transform: translateY(-1px);
            }

            /* Lesson info section */
            .lesson-info-section {
                background: white;
                padding: 1.5rem 0;
                border-bottom: 1px solid #e9ecef;
            }

            .lesson-info-content {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 2rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .lesson-info-navigation {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .lesson-number-large {
                font-size: 2.5rem;
                font-weight: bold;
                color: #0C1B40;
                margin-right: 1rem;
            }

            .info-nav-button {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                background: var(--primary-blue);
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 0.9rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                text-decoration: none;
            }

       
            .info-nav-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
            }

            .info-nav-button:disabled:hover {
                background: var(--primary-blue);
                transform: none;
            }

            .lesson-meta {
                display: flex;
                align-items: center;
                gap: 2rem;
            }

            .lesson-number {
                font-size: 1rem;
                color: var(--text-light);
                font-weight: 500;
                display: none;
            }

            .lesson-title-main {
                font-size: 1.3rem;
                font-weight: 600;
                color: var(--text-dark);
                margin: 0;

            }

            .module-info {
                font-size: 0.9rem;
                color: var(--text-light);
                font-weight: 500;
                display: none;
            }

            .btn-course-content {
                background: var(--primary-blue);
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 600;
                text-decoration: none;
                transition: var(--transition);
            }

            .btn-course-content:hover {
                background: var(--secondary-blue);
                color: white;
                transform: translateY(-1px);
            }

            /* Main content layout */
            .main-content {
                max-width: 100%;
                margin: 0 auto;
                padding: 2rem;
            }

            .lesson-content-wrapper {
                background: white;
                border-radius: 12px;
                /* padding: 3rem; */
                margin-bottom: 2rem;

            }

            .lesson-header {
                text-align: center;
                margin-bottom: 3rem;
            }

            .lesson-type-badge {
                display: inline-flex;
                align-items: center;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.9rem;
                font-weight: 600;
                margin-bottom: 1rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .lesson-type-badge.lesson-type-video {
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                color: white;
                display: none;
            }

            .lesson-type-badge.lesson-type-quiz {
                background: linear-gradient(135deg, #f39c12, #e67e22);
                color: white;
                display: none;
            }

            .lesson-type-badge.lesson-type-text {
                background: linear-gradient(135deg, #27ae60, #229954);
                color: white;
                display: none;
            }

            .lesson-title {
                font-size: 2rem;
                font-weight: 700;
                color: var(--primary-blue);
                margin-bottom: 1rem;
            }

            .lesson-title-main {
                font-size: 3.8rem;
                font-weight: 700;
                color: var(--primary-blue);
                margin-bottom: 0.5rem;
                text-align: center;
                text-shadow: none;
            }

            .lesson-description {
                font-size: 1.1rem;
                color: var(--text-light);
                line-height: 1.6;
                max-width: 800px;
                margin: 0 auto;
            }

            /* Content sections */
            .content-section {
                margin-bottom: 3rem;
            }

            .content-image {
                text-align: center;
                margin: 2rem 0;
            }

            .content-image img {
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            }

            .content-text {
                font-size: 1.1rem;
                line-height: 1.8;
                color: var(--text-dark);
                margin-bottom: 2rem;
            }

            .content-text p {
                margin-bottom: 1.5rem;
            }

            .content-text strong {
                color: var(--primary-blue);
                font-weight: 600;
            }

            /* Estilos para lecciones de texto */
            .text-lesson-container {
                max-width: 100%;
            }

            .lesson-image-container {
                text-align: center;
                margin: 2rem 0;
                position: relative;
            }

            .lesson-image {
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            }

            .image-caption {
                position: absolute;
                bottom: 10px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.7);
                padding: 0.25rem 0.75rem;
                border-radius: 15px;
            }

            .lesson-text-content {
                font-size: 1.1rem;
                line-height: 1.8;
                color: var(--text-dark);
            }

            .lesson-text-content p {
                margin-bottom: 1.5rem;
            }

            .lesson-text-content strong {
                color: var(--primary-blue);
                font-weight: 600;
            }

            /* Layout imagen + primer párrafo */
            .image-text-layout {
                display: flex;
                gap: 2rem;
                margin-bottom: 2rem;
                align-items: flex-start;
            }

            .image-text-layout .lesson-image-container {
                flex: 0 0 45%;
                margin: 0;
            }

            .image-text-layout .first-paragraph {
                flex: 1;
                font-size: 1.1rem;
                line-height: 1.8;
                color: var(--text-dark);
                margin: 0;
            }

            .remaining-content {
                font-size: 1.1rem;
                line-height: 1.8;
                color: var(--text-dark);
            }

            .remaining-content p {
                margin-bottom: 1.5rem;
            }

            .remaining-content strong {
                color: var(--primary-blue);
                font-weight: 600;
            }

            .highlight-box {
                background: #f8f9fa;
                border-left: 4px solid var(--accent-blue);
                padding: 1.5rem;
                margin: 2rem 0;
                border-radius: 0 8px 8px 0;
            }

            .highlight-box h4 {
                color: var(--primary-blue);
                margin-bottom: 1rem;
                font-weight: 600;
            }

            .highlight-box p {
                margin-bottom: 0.5rem;
            }


            /* Navigation section */
            .lesson-navigation {
                background: white;
                border-radius: 12px;
                padding: 2rem;
                gap: 2rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .nav-button {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem 1.5rem;
                border: none;
                border-radius: 8px;
                font-weight: 600;
                text-decoration: none;
                transition: var(--transition);
                cursor: pointer;
            }

            .nav-button.prev {
                background: #0C1B41;
                color: white;
                border-radius: 30px;
            }

            .nav-button.prev:hover {
                background: #e9ecef;
                color: var(--text-dark);
            }

            .nav-button.next {
                background: #0C1B41;
                color: white;
                border-radius: 30px;
            }

            .nav-button.next:hover {
                background: var(--secondary-blue);
                color: white;
                transform: translateY(-1px);
            }

            .nav-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .nav-button:disabled:hover {
                transform: none;
            }

            .completion-button {
                background: #28a745;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: var(--transition);
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .completion-button:hover:not(:disabled) {
                background: #218838;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            }

            .completion-button:disabled {
                opacity: 0.7;
                cursor: not-allowed;
            }

            .completion-button.completed {
                background: #6c757d;
            }

            .certificate-button {
                background: linear-gradient(135deg, var(--accent-blue), var(--secondary-blue));
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: var(--transition);
                display: none;
                align-items: center;
                gap: 0.5rem;
            }

            .certificate-button:hover {
                background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
            }

            .certificate-button.show {
                display: flex;
            }

            /* Modal Styles */
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .modal-content {
                background: white;
                border-radius: 12px;
                max-width: 800px;
                width: 90%;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1.5rem 2rem;
                border-bottom: 1px solid #e9ecef;
                background: var(--primary-blue);
                color: white;
            }

            .modal-header h3 {
                margin: 0;
                font-size: 1.5rem;
                font-weight: 600;
            }

            .modal-close {
                background: none;
                border: none;
                color: white;
                font-size: 2rem;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: var(--transition);
            }

            .modal-close:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .modal-body {
                padding: 2rem;
                max-height: 60vh;
                overflow-y: auto;
            }

            .course-module {
                margin-bottom: 1.5rem;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                overflow: hidden;
            }

            .module-header {
                background: #f8f9fa;
                padding: 1rem 1.5rem;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
                transition: var(--transition);
            }

            .module-header:hover {
                background: #e9ecef;
            }

            .module-title {
                font-weight: 600;
                color: var(--text-dark);
                margin: 0;
            }

            .module-chevron {
                transition: transform 0.3s ease;
                color: var(--text-light);
            }

            .module-chevron.rotated {
                transform: rotate(90deg);
            }

            .module-lessons {
                background: white;
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;
            }

            .module-lessons.show {
                max-height: 500px;
            }

            .lesson-item {
                padding: 0.75rem 1.5rem;
                border-bottom: 1px solid #f1f3f4;
                display: flex;
                align-items: center;
                gap: 1rem;
                cursor: pointer;
                transition: var(--transition);
            }

            .lesson-item:hover {
                background: #f8f9fa;
            }

            .lesson-item:last-child {
                border-bottom: none;
            }

            .lesson-icon {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .lesson-info {
                flex: 1;
            }

            .lesson-name {
                font-weight: 500;
                color: var(--text-dark);
                margin-bottom: 0.25rem;
            }

            .lesson-type {
                font-size: 0.85rem;
                color: var(--text-light);
                text-transform: capitalize;
            }

            .lesson-status {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .lesson-status.completed {
                color: #28a745;
            }

            .lesson-status.locked {
                color: #6c757d;
            }

            .lesson-status.available {
                color: var(--accent-blue);
            }

            .lesson-counter {
                text-align: center;
                font-size: 1rem;
                color: var(--text-light);
                font-weight: 500;
            }

            /* ===== EXERCISES SYSTEM STYLES ===== */
            .lesson-exercises-container {
        
                padding: 1.5rem;
                margin: 2rem 0;
            
            }

            .exercises-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1.5rem;
                padding-bottom: 1rem;
                border-bottom: 2px solid #e9ecef;
            }

            .exercises-header h3 {
                color: var(--primary-blue);
                font-size: 1.3rem;
                font-weight: 600;
                margin: 0;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .add-exercise-btn {
                background: var(--accent-blue);
                color: white;
                border: none;
                padding: 0.75rem 1.25rem;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: var(--transition);
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
            }

            .add-exercise-btn:hover {
                background: var(--primary-blue);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
            }

            .exercises-list {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .exercise-item {
                background: white;
          
                padding: 1.5rem;
             
                transition: var(--transition);
                position: relative;
            }

            .exercise-item:hover {
                border-color: var(--accent-blue);
            
            }

            .exercise-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 1rem;
            }

            .exercise-title {
                font-size: 1.1rem;
                font-weight: 600;
                color: var(--text-dark);
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .exercise-type-badge {
                background: var(--accent-blue);
                color: white;
                padding: 0.25rem 0.75rem;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 500;
				display: none;
            }

            .exercise-actions {
                display: flex;
                gap: 0.5rem;
            }

            .exercise-action-btn {
                background: none;
                border: 1px solid #dee2e6;
                padding: 0.5rem;
                border-radius: 6px;
                cursor: pointer;
                transition: var(--transition);
                color: var(--text-light);
            }

            .exercise-action-btn:hover {
                background: #f8f9fa;
                color: var(--text-dark);
            }

            .exercise-action-btn.delete:hover {
                background: #dc3545;
                color: white;
                border-color: #dc3545;
            }

            .exercise-description {
                color: var(--text-light);
                margin-bottom: 1rem;
                line-height: 1.6;
            }

            /* Checklist Exercise Styles */
            .checklist-exercise .checklist-items {
                display: flex;
                flex-direction: column;
                gap: 0.75rem;
            }

            .checklist-item {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                padding: 0.75rem;
                background: #f8f9fa;
                border-radius: 6px;
                transition: var(--transition);
            }

            .checklist-item:hover {
                background: #e9ecef;
            }

            .checklist-item input[type="checkbox"] {
                width: 18px;
                height: 18px;
                accent-color: var(--accent-blue);
                cursor: pointer;
            }

            .checklist-item label {
                flex: 1;
                cursor: pointer;
                font-weight: 500;
                color: var(--text-dark);
            }

            .checklist-item.completed {
                background: #d4edda;
                border-left: 3px solid #28a745;
            }

            .checklist-item.completed label {
                text-decoration: line-through;
                color: #6c757d;
            }

            /* Text Field Exercise Styles */
            .textfield-exercise .exercise-input {
                width: 100%;
                min-height: 120px;
                padding: 1rem;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-family: inherit;
                font-size: 1rem;
                line-height: 1.6;
                resize: vertical;
                transition: var(--transition);
            }

            .textfield-exercise .exercise-input:focus {
                outline: none;
                border-color: var(--accent-blue);
                box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
            }

            .textfield-exercise .exercise-input::placeholder {
                color: #adb5bd;
                font-style: italic;
            }

            .exercise-word-count {
                margin-top: 0.5rem;
                text-align: right;
            }

            .exercise-word-count .text-success {
                color: #28a745 !important;
                font-weight: 600;
            }

            .exercise-word-count .text-muted {
                color: #6c757d !important;
            }

            /* Exercise Modal Styles */
            .exercise-type-fields {
                margin-top: 1rem;
            }

            .checklist-item-input {
                display: flex;
                gap: 0.5rem;
                margin-bottom: 0.75rem;
                align-items: center;
            }

            .checklist-item-input input {
                flex: 1;
                padding: 0.75rem;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 0.9rem;
            }

            .remove-item-btn {
                background: #dc3545;
                color: white;
                border: none;
                padding: 0.5rem;
                border-radius: 6px;
                cursor: pointer;
                transition: var(--transition);
                display: flex;
                align-items: center;
                justify-content: center;
                width: 36px;
                height: 36px;
            }

            .remove-item-btn:hover {
                background: #c82333;
                transform: scale(1.05);
            }

            .add-item-btn {
                background: var(--accent-blue);
                color: white;
                border: none;
                padding: 0.75rem 1rem;
                border-radius: 6px;
                cursor: pointer;
                transition: var(--transition);
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 500;
                margin-top: 0.5rem;
            }

            .add-item-btn:hover {
                background: var(--primary-blue);
                transform: translateY(-1px);
            }

            .modal-actions {
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
                margin-top: 2rem;
                padding-top: 1rem;
                border-top: 1px solid #dee2e6;
            }

            .btn-secondary {
                background: #6c757d;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 6px;
                cursor: pointer;
                transition: var(--transition);
                font-weight: 500;
            }

            .btn-secondary:hover {
                background: #5a6268;
                transform: translateY(-1px);
            }

            .btn-primary {
                background: var(--accent-blue);
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 6px;
                cursor: pointer;
                transition: var(--transition);
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .btn-primary:hover {
                background: var(--primary-blue);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
            }

            /* Empty state */
            .exercises-empty {
                text-align: center;
                padding: 2rem;
                color: var(--text-light);
            }

            .exercises-empty i {
                font-size: 3rem;
                margin-bottom: 1rem;
                opacity: 0.5;
            }

            /* Responsive Styles for Exercises */
            @media (max-width: 768px) {
                .lesson-exercises-container {
                    margin: 1rem 0;
                    padding: 1rem;
                }

                .exercises-header {
                    flex-direction: column;
                    gap: 1rem;
                    align-items: stretch;
                }

                .exercises-header h3 {
                    font-size: 1.2rem;
                }

                .add-exercise-btn {
                    width: 100%;
                    justify-content: center;
                }

                .exercise-header {
                    flex-direction: column;
                    gap: 1rem;
                    align-items: stretch;
                }

                .exercise-actions {
                    justify-content: flex-end;
                }

                .checklist-item-input {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .checklist-item-input input {
                    width: 100%;
                }

                .modal-actions {
                    flex-direction: column;
                    gap: 0.75rem;
                }

                .modal-actions button {
                    width: 100%;
                    justify-content: center;
                }
            }

            .completion-button {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                background: var(--success-color);
                color: white;
                border: none;
                padding: 0.75rem 2rem;
                border-radius: 25px;
                font-weight: 600;
                cursor: pointer;
                transition: var(--transition);
            }

            .completion-button:hover {
                background: #218838;
                transform: translateY(-1px);
            }

            .completion-button:disabled {
                background: #6c757d;
                cursor: not-allowed;
            }

            .completion-button:disabled:hover {
                transform: none;
            }

            /* ===== ESTILOS PARA PLANTILLAS DE LECCIONES ===== */

            /* Plantilla Básica */
            .basic-template {
                max-width: 800px;
                margin: 0 auto;
            }

            .lesson-header-basic {
                margin-bottom: 2rem;
                text-align: center;
            }

            .lesson-title-basic {
                font-size: 2.5rem;
                color: var(--primary-blue);
                font-weight: 700;
                margin-bottom: 1rem;
                line-height: 1.2;
            }

            .lesson-content-basic {
                font-size: 1.1rem;
                line-height: 1.8;
                color: #333;
            }

            /* Plantilla Con Imagen Destacada */
            .featured-image-template {
                max-width: 100%;
                margin: 0 auto;
            }

            .lesson-header-featured {
                margin-bottom: 2rem;
                text-align: center;
            }

            .lesson-title-featured {
                font-size: 2.5rem;
                color: var(--primary-blue);
                font-weight: 700;
                margin-bottom: 1rem;
                line-height: 1.2;
            }

            .featured-image-container {
                margin-bottom: 2.5rem;
                text-align: center;
            }

            .featured-image {
                width: 100%;
                max-width: 100%;
                height: auto;
                border-radius: 12px;

            }

            .lesson-content-featured {
                font-size: 1.1rem;
                line-height: 1.8;
                color: #333;
            }

            /* Plantilla Dos Columnas */
            .two-columns-template {
                max-width: 1000px;
                margin: 0 auto;
            }

            .lesson-header-two-columns {
                margin-bottom: 2rem;
                text-align: center;
            }

            .lesson-title-two-columns {
                font-size: 2.5rem;
                color: var(--primary-blue);
                font-weight: 700;
                margin-bottom: 1rem;
                line-height: 1.2;
            }

            .two-columns-container {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 3rem;
                align-items: start;
            }

            .column-image-img {
                width: 100%;
                height: auto;
                border-radius: 12px;

            }

            .column-content {
                font-size: 1.1rem;
                line-height: 1.8;
                color: #333;
            }

            /* Plantilla Hero Image */
            .hero-image-template {
                max-width: 1000px;
                margin: 0 auto;
            }

            .hero-image-container {
                position: relative;
				margin-top: 4.5rem;
                margin-bottom: 3rem;
                border-radius: 12px;
                overflow: hidden;
            }

            .hero-image {
                width: 100%;
                height: 400px;
                object-fit: cover;
            }

            .hero-overlay {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
                padding: 3rem 2rem 2rem;
            }

            .lesson-title-hero {
                font-size: 3rem;
                color: white;
                font-weight: 700;
                margin: 0;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
                line-height: 1.2;
                display: none;
            }

            .lesson-content-hero {
                font-size: 1.1rem;
                line-height: 1.8;
                color: #333;
            }

            /* Plantillas Imagen Mediana */
            .medium-right-template,
            .medium-left-template {
                max-width: 1000px;
                margin: 0 auto;
            }

            .lesson-header-medium {
                margin-bottom: 2rem;
                text-align: center;
            }

            .lesson-title-medium {
                font-size: 2.5rem;
                color: var(--primary-blue);
                font-weight: 700;
                margin-bottom: 1rem;
                line-height: 1.2;
            }

            .medium-container {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 2.5rem;
                align-items: center;
            }

            .medium-image {
                width: 100%;
                height: auto;
                border-radius: 12px;

            }

            .medium-content-right,
            .medium-content-left {
                font-size: 1.1rem;
                line-height: 1.8;
                color: #333;
            }

            /* Triple Layout Template Styles */
            .triple-layout-template {
                max-width: 75%;
                margin: 0 auto;
                padding: 0;
            }

            .triple-hero-section {
                margin-bottom: 4rem;
            }

            .triple-hero-section .hero-image-container {
                position: relative;
                height: 100%;
                overflow: hidden;
                border-radius: 16px;
                margin-bottom: 2rem;
            }

            .triple-hero-section .hero-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .triple-hero-section .hero-overlay {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
                padding: 3rem 2rem 2rem;
                color: white;
            }

            .triple-hero-section .lesson-title-hero {
                font-size: 3rem;
                font-weight: 700;
                margin: 0;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            }

            .triple-hero-section .hero-content {
                background: white;
                padding: 2rem;
                border-radius: 12px;
                font-size: 25px;
            }

            .triple-left-section,
            .triple-right-section {
                margin-bottom: 4rem;
                padding: 2rem;

                border-radius: 16px;
            }

            .triple-left-section .section-container,
            .triple-right-section .section-container {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 3rem;
                align-items: center;
            }

            .section-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 12px;

            }

            .section-content-left,
            .section-content-right {
                font-size: 25px;
                line-height: 1.8;
                color: #333;
            }

            /* Responsive para plantillas */
            @media (max-width: 768px) {

                .lesson-title-basic,
                .lesson-title-featured,
                .lesson-title-two-columns,
                .lesson-title-medium {
                    font-size: 2rem;
                }

                .lesson-title-hero {
                    font-size: 2.2rem;
                }

                .two-columns-container,
                .medium-container {
                    grid-template-columns: 1fr;
                    gap: 2rem;
                }

                /* Triple Layout Responsive */
                .triple-hero-section .lesson-title-hero {
                    font-size: 2rem;
                }

                .triple-left-section .section-container,
                .triple-right-section .section-container {
                    grid-template-columns: 1fr;
                    gap: 2rem;
                }

                .section-image {
                    height: 250px;
                }

                .hero-image {
                    height: 250px;
                }

                .hero-overlay {
                    padding: 2rem 1.5rem 1.5rem;
                }

                .lesson-content-basic,
                .lesson-content-featured,
                .column-content,
                .lesson-content-hero,
                .medium-content-right,
                .medium-content-left {
                    font-size: 1rem;
                    line-height: 1.7;
                }
            }

            @media (max-width: 480px) {

                .lesson-title-basic,
                .lesson-title-featured,
                .lesson-title-two-columns,
                .lesson-title-medium {
                    font-size: 1.8rem;
                }

                .lesson-title-hero {
                    font-size: 2rem;
                }

                .hero-image {
                    height: 200px;
                }
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .header-content {
                    flex-direction: column;
                    gap: 1rem;
                    text-align: center;
                }

                .course-info {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .course-title {
                    font-size: 1rem;
                }

                .progress-bar {
                    width: 150px;
                }

                .lesson-info-content {
                    flex-direction: column;
                    gap: 1rem;
                    text-align: center;
                }

                .lesson-meta {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .main-content {
                    padding: 1rem;
                }

                .lesson-content-wrapper {
                    padding: 2rem 1.5rem;
                }

                .lesson-title {
                    font-size: 1.5rem;
                }

                .content-image img {
                    max-width: 90%;
                }

                /* Responsive para layout imagen + texto */
                .image-text-layout {
                    flex-direction: column;
                    gap: 1rem;
                }

                .image-text-layout .lesson-image-container {
                    flex: none;
                }

                .lesson-navigation {
                    flex-direction: column;
                    gap: 2rem;
                }

                .nav-button {
                    width: 100%;
                    justify-content: center;
                }

                .completion-button {
                    width: 100%;
                    justify-content: center;
                }

                .certificate-button {
                    width: 100%;
                    justify-content: center;
                    margin-top: 0.5rem;
                }
            }



            /* Loading states */
            .loading-state {
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 200px;
                flex-direction: column;
                gap: 1rem;
            }

            .spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid var(--primary-blue);
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }

                100% {
                    transform: rotate(360deg);
                }
            }

            /* Video container */
            .video-container {
                position: relative;
                width: 100%;
                max-width: 800px;
                margin: 2rem auto;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

            .video-container iframe {
                width: 100%;
                height: 450px;
                border: none;
            }

            /* Quiz styles */
            .quiz-container {
                background: #f8f9fa;
                border-radius: 12px;
                padding: 2rem;
                margin: 2rem 0;
            }

            .quiz-question {
                font-size: 1.2rem;
                font-weight: 600;
                color: var(--primary-blue);
                margin-bottom: 1.5rem;
            }

            .quiz-options {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .quiz-option {
                background: white;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 1rem;
                cursor: pointer;
                transition: var(--transition);
            }

            .quiz-option:hover {
                border-color: var(--accent-blue);
                background: #f8f9fa;
            }

            .quiz-option.selected {
                border-color: var(--accent-blue);
                background: rgba(74, 144, 226, 0.1);
            }

            /* Notification styles */
            .notification {
                position: fixed;
                top: 100px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1001;
                transform: translateX(400px);
                transition: transform 0.3s ease;
            }

            .notification.show {
                transform: translateX(0);
            }

            .notification.success {
                background: var(--success-color);
            }

            .notification.error {
                background: #dc3545;
            }

            .notification.warning {
                background: #ffc107;
                color: var(--text-dark);
            }

            /* Quiz Styles */
            .quiz-lesson-container {
                max-width: 800px;
                margin: 0 auto;
            }

            .quiz-intro {
                text-align: center;
                padding: 2rem;
                background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
                border-radius: 12px;
                color: white;
                margin-bottom: 2rem;
            }

            .quiz-intro-content h3 {
                font-size: 1.8rem;
                margin-bottom: 1rem;
                font-weight: 700;
            }

            .quiz-description {
                font-size: 1.1rem;
                margin-bottom: 2rem;
                opacity: 0.9;
            }

            .quiz-stats {
                display: flex;
                justify-content: center;
                gap: 2rem;
                margin-bottom: 2rem;
                flex-wrap: wrap;
            }

            .stat-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 1rem;
                font-weight: 500;
            }

            .start-quiz-btn {
                padding: 1rem 2rem;
                font-size: 1.1rem;
                font-weight: 600;
                border-radius: 8px;
                border: none;
                cursor: pointer;
                transition: var(--transition);
            }

            .start-quiz-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }

            .quiz-content {
                background: white;
                border-radius: 12px;
                padding: 2rem;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            .quiz-header {
                margin-bottom: 2rem;
            }

            .quiz-progress {
                background: #e9ecef;
                height: 8px;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 1rem;
            }

            .progress-bar {
                background: linear-gradient(90deg, var(--primary-blue), var(--accent-blue));

                transition: width 0.3s ease;
            }

            .question-counter {
                text-align: center;
                font-weight: 600;
                color: var(--text-light);
            }

            .quiz-question-content {
                margin-bottom: 2rem;
            }

            .question-text {
                font-size: 1.3rem;
                font-weight: 600;
                color: var(--text-dark);
                margin-bottom: 1rem;
                line-height: 1.4;
            }

            .instruction-text {
                color: var(--text-light);
                margin-bottom: 1.5rem;
                font-style: italic;
            }

            .quiz-options {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .quiz-option {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 1rem 1.5rem;
                cursor: pointer;
                transition: var(--transition);
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            .quiz-option:hover {
                border-color: var(--accent-blue);
                background: #f0f8ff;
            }

            .quiz-option.selected {
                border-color: var(--primary-blue);
                background: #e3f2fd;
            }

            .quiz-option.correct {
                border-color: #28a745;
                background: #d4edda;
                animation: correctPulse 0.6s ease-in-out;
            }

            .quiz-option.incorrect {
                border-color: #dc3545;
                background: #f8d7da;
                animation: incorrectShake 0.6s ease-in-out;
            }

            .quiz-option.disabled {
                opacity: 0.6;
                cursor: not-allowed;
                pointer-events: none;
            }

            @keyframes correctPulse {
                0% {
                    transform: scale(1);
                }

                50% {
                    transform: scale(1.02);
                    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
                }

                100% {
                    transform: scale(1);
                }
            }

            @keyframes incorrectShake {

                0%,
                100% {
                    transform: translateX(0);
                }

                10%,
                30%,
                50%,
                70%,
                90% {
                    transform: translateX(-5px);
                }

                20%,
                40%,
                60%,
                80% {
                    transform: translateX(5px);
                }
            }

            .quiz-feedback {
                margin-top: 1rem;
                padding: 1rem;
                border-radius: 8px;
                font-weight: 500;
                display: none;
            }

            .quiz-feedback.correct {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }

            .quiz-feedback.incorrect {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }

            .quiz-option input {
                margin: 0;
            }

            .option-text {
                flex: 1;
                font-size: 1rem;
                line-height: 1.4;
            }

            .quiz-navigation {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 2rem;
                padding-top: 2rem;
                border-top: 1px solid #e9ecef;
            }

            .quiz-results {
                background: white;
                border-radius: 12px;
                padding: 2rem;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                text-align: center;
            }

            .results-header {
                margin-bottom: 2rem;
            }

            .results-header.passed {
                color: #28a745;
            }

            .results-header.failed {
                color: #dc3545;
            }

            .score-circle {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                font-size: 2rem;
                font-weight: 700;
                color: white;
                display: none;
            }

            .results-header.passed .score-circle {
                background: linear-gradient(135deg, #28a745, #20c997);
            }

            .results-header.failed .score-circle {
                background: linear-gradient(135deg, #dc3545, #fd7e14);
            }

            .results-actions {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-top: 2rem;
            }

            /* ===== INLINE EDITING STYLES ===== */
            .admin-edit-indicator {
                position: relative;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .admin-edit-indicator:hover {
                background-color: rgba(30, 136, 229, 0.1);
                border-radius: 4px;
                padding: 2px 4px;
            }

            .admin-edit-indicator::after {
                content: '✏️';
                position: absolute;
                top: -8px;
                right: -8px;
                background: #1e88e5;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                font-size: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 10;
            }

            .admin-edit-indicator:hover::after {
                opacity: 1;
            }

            .inline-editor {
                position: relative;
                background: white;
                border: 2px solid #1e88e5;
                border-radius: 8px;
                padding: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
            }

            .inline-editor textarea,
            .inline-editor input {
                width: 100%;
                border: none;
                outline: none;
                resize: vertical;
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
                background: transparent;
            }

            .inline-editor-actions {
                display: flex;
                gap: 8px;
                margin-top: 8px;
                justify-content: flex-end;
            }

            .inline-editor-btn {
                padding: 6px 12px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            .inline-editor-btn.save {
                background: #28a745;
                color: white;
            }

            .inline-editor-btn.save:hover {
                background: #218838;
            }

            .inline-editor-btn.cancel {
                background: #6c757d;
                color: white;
            }

            .inline-editor-btn.cancel:hover {
                background: #5a6268;
            }

            .inline-editor-loading {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255,255,255,0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
            }

            .admin-edit-success {
                animation: editSuccess 2s ease-in-out;
            }

            @keyframes editSuccess {
                0% { background-color: transparent; }
                50% { background-color: rgba(40, 167, 69, 0.2); }
                100% { background-color: transparent; }
            }

            /* ===== FLOATING ADD BUTTON WITH DROPDOWN ===== */
            .floating-add-container {
                position: fixed;
                bottom: 30px;
                right: 30px;
                z-index: 1000;
            }

            .floating-add-btn {
                width: 60px;
                height: 60px;
                background: var(--primary-blue);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
                font-size: 24px;
            }

            .floating-add-btn:hover {
                transform: translateY(-2px) rotate(45deg);
                box-shadow: 0 6px 16px rgba(0,0,0,0.4);
                background: var(--secondary-blue);
            }

            .floating-add-menu {
                position: absolute;
                bottom: 70px;
                right: 0;
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                padding: 8px;
                min-width: 160px;
                opacity: 0;
                visibility: hidden;
                transform: translateY(10px);
                transition: all 0.3s ease;
            }

            .floating-add-menu.show {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }

            .add-menu-item {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                color: #333;
                font-weight: 500;
                font-size: 14px;
            }

            .add-menu-item:hover {
                background: #f0f8ff;
                color: var(--primary-blue);
            }

            .add-menu-item i {
                margin-right: 12px;
                font-size: 16px;
                width: 20px;
                text-align: center;
            }

            /* Admin-specific styles - hide progress elements */
            <?php if (current_user_can('manage_options')): ?>
            .completion-button,
            .certificate-button,
            .lesson-lock-icon,
            .bi-lock,
            .bi-lock-fill,
            .progress-indicator,
            .lesson-progress {
                display: none !important;
            }

            .lesson-item.locked,
            .lesson-locked {
                opacity: 1 !important;
                pointer-events: auto !important;
            }

            .lesson-item.locked::after {
                display: none !important;
            }
            <?php endif; ?>

            /* Add lesson modal */
            .add-lesson-modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 2000;
                align-items: center;
                justify-content: center;
            }

            .add-lesson-modal-content {
                background: white;
                border-radius: 12px;
                width: 95%;
                max-width: 1000px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                display: flex;
                flex-direction: row;
            }

            .modal-left-panel {
                width: 300px;
                background: #f8f9fa;
                padding: 20px;
                border-radius: 12px 0 0 12px;
                border-right: 1px solid #e9ecef;
            }

            .modal-right-panel {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }

            .modal-header {
                margin-bottom: 20px;
            }

            .modal-header h3 {
                color: var(--primary-blue);
                margin: 0;
                font-size: 20px;
                font-weight: 600;
            }

            .form-group {
                margin-bottom: 15px;
            }

            .form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: 600;
                color: #333;
                font-size: 14px;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                box-sizing: border-box;
            }

            .form-group select {
                background: white;
                cursor: pointer;
            }

            .form-group select:focus {
                border-color: var(--primary-blue);
                outline: none;
                box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
            }

            /* Template selector grid */
            .template-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
                margin-top: 10px;
            }

            .template-option {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px;
                text-align: center;
                cursor: pointer;
                transition: all 0.3s ease;
                background: white;
            }

            .template-option:hover {
                border-color: var(--primary-blue);
                background: #f0f8ff;
            }

            .template-option.selected {
                border-color: var(--primary-blue);
                background: #e3f2fd;
            }

            .template-icon {
                font-size: 24px;
                margin-bottom: 5px;
            }

            .template-name {
                font-weight: 600;
                font-size: 12px;
                color: #333;
                margin-bottom: 2px;
            }

            .template-desc {
                font-size: 10px;
                color: #666;
                line-height: 1.2;
            }

            /* Template preview area */
            .template-preview-area {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 20px;
                margin-top: 20px;
                min-height: 300px;
            }

            .template-preview-area h4 {
                margin: 0 0 15px 0;
                color: var(--primary-blue);
                font-size: 16px;
                font-weight: 600;
            }

            .template-preview-content {
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 20px;
                min-height: 250px;
                overflow-y: auto;
            }

            .section-group {
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
            }

            .section-group h5 {
                margin: 0 0 10px 0;
                color: #333;
                font-size: 14px;
                font-weight: 600;
            }

            .section-group textarea {
                width: 100%;
                min-height: 80px;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 14px;
                resize: vertical;
                box-sizing: border-box;
            }

            .image-upload-group {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
            }

            .image-upload-btn {
                background: var(--primary-blue);
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
            }

            .image-upload-btn:hover {
                background: var(--secondary-blue);
            }

            .modal-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-top: 20px;
                padding-top: 20px;
                border-top: 1px solid #e9ecef;
            }

            .modal-actions button {
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 14px;
            }

            .modal-actions button[type="submit"] {
                background: var(--primary-blue);
                color: white;
                border: none;
            }

            .modal-actions button[type="button"] {
                background: #f8f9fa;
                color: #333;
                border: 1px solid #dee2e6;
            }

            .modal-actions button:hover {
                transform: translateY(-1px);
            }

            /* New module button */
            .new-module-item {
                margin-top: 10px;
            }

            .new-module-button {
                background: rgba(0, 123, 255, 0.2);
                border: 2px dashed #007bff;
                border-radius: 8px;
                padding: 15px;
                text-align: center;
                color: #007bff;
                cursor: pointer;
                transition: all 0.3s ease;
                font-weight: 600;
                margin: 10px 0;
            }

            .new-module-button:hover {
                background: rgba(0, 123, 255, 0.3);
                border-color: #0056b3;
                color: #0056b3;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
            }

            .new-module-button i {
                font-size: 18px;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .main-content {
                    padding: 1rem;
                }

                .lesson-content-wrapper {
                    padding: 2rem 1.5rem;
                }

                .lesson-navigation {
                    flex-direction: column;
                    gap: 1rem;
                }

                .nav-button {
                    width: 100%;
                    justify-content: center;
                }

                .lesson-counter {
                    order: -1;
                    text-align: center;
                }

                .quiz-stats {
                    flex-direction: column;
                    gap: 1rem;
                }

                .quiz-navigation {
                    flex-direction: column;
                    gap: 1rem;
                }

                .results-actions {
                    flex-direction: column;
                }
            }

            /* Course Completion Modal & Certificate Styles */
            .completion-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .completion-modal-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .completion-modal-content {
                background: white;
                border-radius: 20px;
                max-width: 900px;
                width: 95%;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                transform: scale(0.8) translateY(50px);
                transition: all 0.3s ease;
            }

            .completion-modal-overlay.show .completion-modal-content {
                transform: scale(1) translateY(0);
            }

            .completion-celebration {
                position: relative;
                padding: 3rem 2rem;
                text-align: center;
                overflow: hidden;
            }

            /* Confetti Animation */
            .confetti-container {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                overflow: hidden;
            }

            .confetti {
                position: absolute;
                width: 10px;
                height: 10px;
                background: var(--accent-yellow);
                animation: confetti-fall 3s linear infinite;
            }

            .confetti:nth-child(1) {
                left: 10%;
                animation-delay: 0s;
                background: var(--accent-blue);
            }

            .confetti:nth-child(2) {
                left: 20%;
                animation-delay: 0.2s;
                background: var(--accent-yellow);
            }

            .confetti:nth-child(3) {
                left: 30%;
                animation-delay: 0.4s;
                background: var(--primary-blue);
            }

            .confetti:nth-child(4) {
                left: 40%;
                animation-delay: 0.6s;
                background: var(--secondary-blue);
            }

            .confetti:nth-child(5) {
                left: 50%;
                animation-delay: 0.8s;
                background: var(--accent-blue);
            }

            .confetti:nth-child(6) {
                left: 60%;
                animation-delay: 1s;
                background: var(--accent-yellow);
            }

            .confetti:nth-child(7) {
                left: 70%;
                animation-delay: 1.2s;
                background: var(--primary-blue);
            }

            .confetti:nth-child(8) {
                left: 80%;
                animation-delay: 1.4s;
                background: var(--secondary-blue);
            }

            .confetti:nth-child(9) {
                left: 90%;
                animation-delay: 1.6s;
                background: var(--accent-blue);
            }

            .confetti:nth-child(10) {
                left: 95%;
                animation-delay: 1.8s;
                background: var(--accent-yellow);
            }

            @keyframes confetti-fall {
                0% {
                    transform: translateY(-100vh) rotate(0deg);
                    opacity: 1;
                }

                100% {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }

            .completion-header {
                margin-bottom: 2rem;
                position: relative;
                z-index: 1;
            }

            .completion-icon {
                font-size: 4rem;
                margin-bottom: 1rem;
                animation: bounce 2s infinite;
            }

            @keyframes bounce {

                0%,
                20%,
                50%,
                80%,
                100% {
                    transform: translateY(0);
                }

                40% {
                    transform: translateY(-20px);
                }

                60% {
                    transform: translateY(-10px);
                }
            }

            .completion-header h2 {
                font-size: 2.5rem;
                font-weight: 700;
                color: var(--primary-blue);
                margin-bottom: 0.5rem;
                font-family: 'Outfit', sans-serif;
            }

            .completion-header p {
                font-size: 1.2rem;
                color: var(--text-light);
                margin: 0;
            }

            /* Certificate Styles */
            .certificate-container {
                margin: 2rem 0;
                position: relative;
                z-index: 1;
            }

            .certificate {
                background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                border-radius: 15px;
                padding: 2rem;
                margin: 0 auto;
                max-width: 700px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                position: relative;
            }

            .certificate-border {
                border: 3px solid;
                border-image: linear-gradient(135deg, var(--primary-blue), var(--accent-blue), var(--secondary-blue)) 1;
                border-radius: 12px;
                padding: 2rem;
                position: relative;
            }

            .certificate-border::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(135deg, var(--primary-blue), var(--accent-blue), var(--secondary-blue));
                border-radius: 15px;
                z-index: -1;
            }

            .certificate-content {
                background: white;
                border-radius: 8px;
                padding: 2rem;
                text-align: center;
            }

            .certificate-header {
                margin-bottom: 2rem;
            }

            .certificate-header h3 {
                font-size: 1.8rem;
                font-weight: 700;
                color: var(--primary-blue);
                margin-bottom: 1rem;
                letter-spacing: 2px;
                text-transform: uppercase;
            }

            .certificate-logo {
                font-size: 3rem;
                color: var(--accent-yellow);
            }

            .certificate-body {
                margin: 2rem 0;
            }

            .certificate-text {
                font-size: 1.1rem;
                color: var(--text-light);
                margin: 0.5rem 0;
            }

            .student-name,
            .course-name {
                font-size: 1.8rem;
                font-weight: 700;
                color: var(--primary-blue);
                margin: 1rem 0;
                text-decoration: underline;
                text-decoration-color: var(--accent-blue);
                text-underline-offset: 8px;
            }

            .completion-date {
                font-size: 1rem;
                color: var(--text-light);
                margin-top: 1.5rem;
            }

            .certificate-footer {
                margin-top: 2rem;
                padding-top: 1.5rem;
                border-top: 2px solid var(--accent-blue);
            }

            .signature-line {
                text-align: center;
            }

            .signature {
                font-size: 1.3rem;
                font-weight: 600;
                color: var(--primary-blue);
                margin-bottom: 0.5rem;
            }

            .signature-title {
                font-size: 0.9rem;
                color: var(--text-light);
                text-transform: uppercase;
                letter-spacing: 1px;
            }

            /* Completion Actions */
            .completion-actions {
                display: flex;
                gap: 1rem;
                justify-content: center;
                flex-wrap: wrap;
                margin-top: 2rem;
                position: relative;
                z-index: 1;
            }

            .btn-download-certificate,
            .btn-share-achievement,
            .btn-close-modal {
                padding: 0.75rem 2rem;
                border: none;
                border-radius: 25px;
                font-weight: 600;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }

            .btn-download-certificate {
                background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
                color: white;
            }

            .btn-download-certificate:hover {
                background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(12, 27, 65, 0.3);
            }

            .btn-share-achievement {
                background: linear-gradient(135deg, var(--accent-blue), var(--secondary-blue));
                color: white;
            }

            .btn-share-achievement:hover {
                background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
            }

            .btn-close-modal {
                background: var(--accent-yellow);
                color: var(--primary-blue);
            }

            .btn-close-modal:hover {
                background: #ffb300;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
            }

            /* Responsive Design for Certificate Modal */
            @media (max-width: 768px) {
                .completion-modal-content {
                    width: 98%;
                    margin: 1rem;
                }

                .completion-celebration {
                    padding: 2rem 1rem;
                }

                .completion-header h2 {
                    font-size: 2rem;
                }

                .certificate {
                    padding: 1rem;
                }

                .certificate-border {
                    padding: 1rem;
                }

                .certificate-content {
                    padding: 1.5rem;
                }

                .certificate-header h3 {
                    font-size: 1.4rem;
                }

                .student-name,
                .course-name {
                    font-size: 1.4rem;
                }

                .completion-actions {
                    flex-direction: column;
                    align-items: center;
                }

                .btn-download-certificate,
                .btn-share-achievement,
                .btn-close-modal {
                    width: 100%;
                    max-width: 300px;
                    justify-content: center;
                }
            }

            /* estado base: invisible */
            .overlay-next-lesson {
                position: fixed;
                top: 20%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.7);
                color: #fff;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 16px;
                z-index: 9999;
                opacity: 0;
                /* empieza transparente */
                transition: opacity 1s ease;
                /* duración de fade‑in/out */
                pointer-events: none;
            }

            /* clase que activa el fade‑in */
            .overlay-next-lesson.show {
                opacity: 1;
            }

            /* clase que activa el fade‑out */
            .overlay-next-lesson.hide {
                opacity: 0;
            }
        </style>

    </head>

    <body>
        <?php if ($access_denied): ?>
            <!-- Estado de acceso denegado - Verificación Robusta -->
            <div class="container-fluid d-flex justify-content-center align-items-center min-vh-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                <div class="text-center" style="max-width: 600px; padding: 3rem; background: white; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">

                    <?php if ($user_status === 'guest'): ?>
                        <!-- Usuario no logueado -->
                        <div class="mb-4">
                            <i class="bi bi-person-x-fill" style="font-size: 4rem; color: #ffc107;"></i>
                        </div>
                        <h2 class="mb-3" style="color: var(--primary-blue);">Sign In Required</h2>
                        <p class="mb-4" style="color: var(--text-light); font-size: 1.1rem;"><?php echo esc_html($access_message); ?></p>
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="<?php echo esc_url($redirect_url); ?>" class="btn btn-lg" style="background: var(--primary-blue); color: white; padding: 0.75rem 2rem; border-radius: 25px;">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                            </a>
                            <a href="/register/" class="btn btn-lg btn-outline-secondary" style="padding: 0.75rem 2rem; border-radius: 25px;">
                                <i class="bi bi-person-plus me-2"></i>Create Account
                            </a>
                        </div>

                    <?php elseif ($user_status === 'logged_in_not_enrolled'): ?>
                        <!-- Usuario logueado pero no inscrito -->
                        <div class="mb-4">
                            <i class="bi bi-cart-x-fill" style="font-size: 4rem; color: #dc3545;"></i>
                        </div>
                        <h2 class="mb-3" style="color: var(--primary-blue);">Course Purchase Required</h2>
                        <p class="mb-4" style="color: var(--text-light); font-size: 1.1rem;"><?php echo esc_html($access_message); ?></p>
                        <div class="alert alert-info mb-4" style="border-radius: 15px;">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Hello <?php echo esc_html($current_user->display_name); ?>!</strong>
                            You're signed in but need to purchase this course to access the lessons.
                        </div>
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="<?php echo esc_url($redirect_url); ?>" class="btn btn-lg" style="background: var(--accent-blue); color: white; padding: 0.75rem 2rem; border-radius: 25px;">
                                <i class="bi bi-cart-plus me-2"></i>Purchase Course
                            </a>
                            <a href="/online-courses/" class="btn btn-lg btn-outline-secondary" style="padding: 0.75rem 2rem; border-radius: 25px;">
                                <i class="bi bi-grid me-2"></i>Browse All Courses
                            </a>
                        </div>

                    <?php else: ?>
                        <!-- Error genérico -->
                        <div class="mb-4">
                            <i class="bi bi-exclamation-triangle-fill" style="font-size: 4rem; color: #ffc107;"></i>
                        </div>
                        <h2 class="mb-3" style="color: var(--primary-blue);">Access Restricted</h2>
                        <p class="mb-4" style="color: var(--text-light); font-size: 1.1rem;"><?php echo esc_html($access_message); ?></p>
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="/online-courses/" class="btn btn-lg" style="background: var(--primary-blue); color: white; padding: 0.75rem 2rem; border-radius: 25px;">
                                <i class="bi bi-house me-2"></i>Go to Courses
                            </a>
                        </div>
                    <?php endif; ?>

                    <!-- Información adicional -->
                    <div class="mt-4 pt-3" style="border-top: 1px solid #e9ecef;">
                        <small class="text-muted">
                            <i class="bi bi-shield-check me-1"></i>
                            Secure access verification by ASG Learning Platform
                        </small>
                    </div>
                </div>
            </div>

            <!-- Script para redirección automática opcional -->
            <script>
                // Redirección automática después de 10 segundos (opcional)
                // setTimeout(() => {
                //     window.location.href = '<?php echo esc_js($redirect_url); ?>';
                // }, 10000);
            </script>
        <?php else: ?>

            <!-- Header principal -->
            <header class="main-header">
                <div class="header-content">
                    <div class="course-info">
                        <a href="<?php echo current_user_can('manage_options') ? '/editor-dashboard/' : '/my-programs/'; ?>" class="back-arrow">
                            <i class="bi bi-arrow-left"></i>
                        </a>
                        <h1 class="course-title" id="courseTitle">Loading course...</h1>
                    </div>

                    <div class="progress-section">
                        <div class="progress-container">
                            <span class="progress-text">Progress</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <span class="progress-text" id="progressText">45%</span>
                        </div>
                    </div>

                    <div class="user-section">
                        <span class="username" id="username">Username</span>
                        <i class="bi bi-person-circle" style="font-size: 1.5rem;"></i>
                        <a href="<?php echo current_user_can('manage_options') ? '/editor-dashboard/' : '/my-programs/'; ?>" class="btn-courses">
                            <?php echo current_user_can('manage_options') ? 'Go to Dashboard' : 'Go to my Courses'; ?>
                        </a>
                    </div>
                </div>
            </header>

            <!-- Lesson info section -->
            <section class="lesson-info-section">
                <div class="lesson-info-content">
                    <div class="lesson-meta">
                        <span class="lesson-number" id="lessonNumber">Lesson N.1</span>
                        <span class="module-info" id="moduleInfo">Module 1.1</span>
                    </div>


                    <!-- Navigation buttons in lesson-info-section -->
                    <div class="lesson-info-navigation">
                        <div class="lesson-number-large" id="lessonNumberLarge">Lesson: #</div>
                        <button class="info-nav-button prev" id="btnPrevLessonInfo">
                            <i class="bi bi-arrow-left"></i>
                            Previous
                        </button>
                        <button class="info-nav-button next" id="btnNextLessonInfo">
                            Next
                            <i class="bi bi-arrow-right"></i>
                        </button>
                    </div>

                    <button class="btn-course-content" id="btnCourseContent">See Course Content</button>
                </div>
            </section>

            <!-- Main content -->
            <main class="main-content">
                <div class="lesson-content-wrapper">
                    <div id="lessonContentArea">
                        <div class="loading-state">
                            <div class="spinner"></div>
                            <span>Loading Content...</span>
                        </div>
                    </div>
                </div>

                <!-- Exercises Section -->
                <?php if (current_user_can('manage_options')): ?>
                <!-- Always show for admins -->
                <div class="lesson-exercises-container" id="lessonExercisesContainer">
                <?php else: ?>
                <!-- Hide for students until exercises exist -->
                <div class="lesson-exercises-container" id="lessonExercisesContainer" style="display: none;">
                <?php endif; ?>
                    <div class="exercises-header">
                        <h3><i class="bi bi-clipboard-check"></i>Interactive Exercise</h3>
                        <?php if (current_user_can('manage_options')): ?>
                        <button class="add-exercise-btn" onclick="openAddExerciseModal()">
                            <i class="bi bi-plus-circle"></i> Add Exercise to this lesson
                        </button>
                        <?php endif; ?>
                    </div>
                    <div class="exercises-list" id="exercisesList">
                        <!-- Exercises will be loaded here -->
                    </div>
                </div>

                <!-- Navigation -->
                <div class="lesson-counter" id="lessonCounter">1 of 3 Lessons</div>
                <nav class="lesson-navigation">
                    <button class="nav-button prev" id="btnPrevLesson">
                        <i class="bi bi-arrow-left"></i>
                        Previous Lesson
                    </button>
                    <button class="nav-button next" id="btnNextLesson">
                        Next Lesson
                        <i class="bi bi-arrow-right"></i>
                    </button>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <button class="completion-button" id="btnMarkComplete">
                            <i class="bi bi-check-circle"></i>
                            Mark as Complete
                        </button>

                        <button class="certificate-button" id="btnViewCertificate" onclick="showCourseCompletionModal()">
                            <i class="bi bi-award"></i>
                            View Certificate
                        </button>
                    </div>
                </nav>
            </main>

            <!-- Modal de contenido del curso -->
            <div id="courseContentModal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Course Content</h3>
                        <button class="modal-close" onclick="closeCourseContentModal()">&times;</button>
                    </div>
                    <div class="modal-body" id="courseContentBody">
                        <div class="loading-state">
                            <div class="spinner"></div>
                            <span>Loading course content...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notificaciones -->
            <div id="notificationContainer"></div>

            <!-- Admin Add Content Button with Dropdown (solo visible para admins) -->
            <?php if (current_user_can('manage_options')): ?>
            <div class="floating-add-container">
                <div class="floating-add-btn" id="mainAddBtn" onclick="toggleAddMenu()" title="Add New Content">
                    <i class="tools">Admin Tools</i>
                </div>
                <div class="floating-add-menu" id="addMenu">
                    <div class="add-menu-item" onclick="showAddModuleModal()" title="Create New Module">
                        <i class="bi bi-collection"></i>
                        <span>New Module</span>
                    </div>
                    <div class="add-menu-item" onclick="showAddLessonModal()" title="Create New Lesson">
                        <i class="bi bi-file-text"></i>
                        <span>New Lesson</span>
                    </div>
                </div>
            </div>

            <!-- Add Module Modal -->
            <div class="add-lesson-modal" id="addModuleModal">
                <div class="add-lesson-modal-content">
                    <!-- Left Panel -->
                    <div class="modal-left-panel">
                        <div class="modal-header">
                            <h3>New Module</h3>
                        </div>

                        <form id="addModuleForm">
                            <div class="form-group">
                                <label for="moduleTitle">Module Title *</label>
                                <input type="text" id="moduleTitle" name="title" required>
                            </div>

                            <div class="form-group">
                                <label for="moduleDescription">Description</label>
                                <textarea id="moduleDescription" name="description" rows="4" placeholder="Brief description of this module..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="moduleOrder">Order</label>
                                <input type="number" id="moduleOrder" name="order" min="1" value="1">
                            </div>

                            <div class="form-group">
                                <label for="modulePreview">
                                    <input type="checkbox" id="modulePreview" name="is_preview" style="width: auto; margin-right: 8px;">
                                    Preview Module (accessible without enrollment)
                                </label>
                            </div>
                        </form>
                    </div>

                    <!-- Right Panel -->
                    <div class="modal-right-panel">
                        <div class="template-preview-area">
                            <h4>Module Preview</h4>
                            <div id="modulePreviewContent" class="template-preview-content">
                                <div style="text-align: center; padding: 40px 20px; color: #666;">
                                    <i class="bi bi-collection text-4xl mb-3 block" style="font-size: 48px;"></i>
                                    <h4 style="margin: 0 0 10px 0; color: #333;">[Module Title]</h4>
                                    <p style="margin: 0; font-size: 14px;">This module will contain lessons and organize your course content.</p>
                                </div>
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button type="button" onclick="closeAddModuleModal()">Cancel</button>
                            <button type="submit" form="addModuleForm">
                                <i class="bi bi-collection me-1"></i>
                                Create Module
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Lesson Modal -->
            <div class="add-lesson-modal" id="addLessonModal">
                <div class="add-lesson-modal-content">
                    <!-- Left Panel -->
                    <div class="modal-left-panel">
                        <div class="modal-header">
                            <h3>Nueva Lección</h3>
                        </div>

                        <form id="addLessonForm">
                            <div class="form-group">
                                <label for="lessonTitle">Lesson Title *</label>
                                <input type="text" id="lessonTitle" name="title" required>
                            </div>

                            <div class="form-group">
                                <label for="lessonModule">Target Module *</label>
                                <select id="lessonModule" name="module_id" required>
                                    <!-- Options will be populated dynamically -->
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="lessonType">Lesson Type</label>
                                <select id="lessonType" name="type" onchange="handleLessonTypeChange()">
                                    <option value="text">Text Lesson</option>
                                    <option value="video">Video</option>
                                    <option value="quiz">Quiz</option>
                                </select>
                            </div>

                            <div class="form-group" id="templateSelectorGroup">
                                <label>Lesson Template</label>
                                <div class="template-grid">
                                    <div class="template-option selected" data-template="basic" onclick="selectTemplate('basic')">
                                        <div class="template-icon">📄</div>
                                        <div class="template-name">Basic</div>
                                        <div class="template-desc">Simple text with title</div>
                                    </div>
                                    <div class="template-option" data-template="featured_image" onclick="selectTemplate('featured_image')">
                                        <div class="template-icon">🖼️</div>
                                        <div class="template-name">Featured</div>
                                        <div class="template-desc">Large image + content</div>
                                    </div>
                                    <div class="template-option" data-template="two_columns" onclick="selectTemplate('two_columns')">
                                        <div class="template-icon">📊</div>
                                        <div class="template-name">Two Columns</div>
                                        <div class="template-desc">Image left, text right</div>
                                    </div>
                                    <div class="template-option" data-template="hero_image" onclick="selectTemplate('hero_image')">
                                        <div class="template-icon">🌟</div>
                                        <div class="template-name">Hero Image</div>
                                        <div class="template-desc">Large image + text below</div>
                                    </div>
                                    <div class="template-option" data-template="medium_right" onclick="selectTemplate('medium_right')">
                                        <div class="template-icon">◀️</div>
                                        <div class="template-name">Medium Right</div>
                                        <div class="template-desc">Image + text right</div>
                                    </div>
                                    <div class="template-option" data-template="medium_left" onclick="selectTemplate('medium_left')">
                                        <div class="template-icon">▶️</div>
                                        <div class="template-name">Medium Left</div>
                                        <div class="template-desc">Image + text left</div>
                                    </div>
                                    <div class="template-option" data-template="triple_layout" onclick="selectTemplate('triple_layout')">
                                        <div class="template-icon">🎯</div>
                                        <div class="template-name">Triple Layout</div>
                                        <div class="template-desc">Hero + Left + Right</div>
                                    </div>
                                </div>
                                <input type="hidden" id="selectedTemplate" value="basic">
                            </div>

                            <div class="form-group">
                                <label for="lessonPreview">
                                    <input type="checkbox" id="lessonPreview" name="is_preview" style="width: auto; margin-right: 8px;">
                                    Preview Template
                                </label>
                            </div>
                        </form>
                    </div>

                    <!-- Right Panel -->
                    <div class="modal-right-panel">
                        <div class="template-preview-area">
                            <h4>Preview</h4>
                            <div id="templatePreview" class="template-preview-content">
                                <p class="text-gray-500 text-center py-8">
                                    <i class="bi bi-eye text-3xl mb-2 block"></i>
                                    Select a template to see preview
                                </p>
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button type="button" onclick="closeAddLessonModal()">Cancel</button>
                            <button type="submit" form="addLessonForm">
                                <i class="bi bi-plus-circle me-1"></i>
                                Create Lesson with Placeholders
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Module Modal -->
            <div class="add-lesson-modal" id="addModuleModal">
                <div class="add-lesson-modal-content" style="max-width: 600px;">
                    <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #e9ecef;">
                        <h3 style="margin: 0; color: var(--primary-blue);">
                            <i class="bi bi-collection me-2"></i>Create New Module
                        </h3>
                    </div>

                    <form id="addModuleForm" style="padding: 20px;">
                        <div class="form-group">
                            <label for="moduleTitle">Module Title *</label>
                            <input type="text" id="moduleTitle" name="title" required
                                   placeholder="e.g., Introduction to Digital Marketing">
                        </div>

                        <div class="form-group">
                            <label for="moduleDescription">Module Description</label>
                            <textarea id="moduleDescription" name="description" rows="4"
                                      placeholder="Brief description of what this module covers..."></textarea>
                        </div>

                        <div class="modal-actions">
                            <button type="button" onclick="closeAddModuleModal()">Cancel</button>
                            <button type="submit">
                                <i class="bi bi-plus-circle me-1"></i>
                                Create Module
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>

            <!-- Add Exercise Modal -->
            <div id="addExerciseModal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="bi bi-clipboard-plus"></i> Add Exercise to Lesson</h3>
                        <button class="modal-close" onclick="closeAddExerciseModal()">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>

                    <form id="addExerciseForm" onsubmit="handleAddExercise(event)">
                        <div class="form-group">
                            <label for="exerciseType">Exercise Type</label>
                            <select id="exerciseType" name="exerciseType" required onchange="handleExerciseTypeChange()">
                                <option value="">Select exercise type...</option>
                                <option value="checklist">Checklist Exercise</option>
                                <option value="textfield">Text Field Exercise</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="exerciseTitle">Exercise Title</label>
                            <input type="text" id="exerciseTitle" name="exerciseTitle" required
                                   placeholder="e.g., Key Concepts Review">
                        </div>

                        <div class="form-group">
                            <label for="exerciseDescription">Description (Optional)</label>
                            <textarea id="exerciseDescription" name="exerciseDescription" rows="2"
                                      placeholder="Brief description of what students should do..."></textarea>
                        </div>

                        <!-- Checklist specific fields -->
                        <div id="checklistFields" class="exercise-type-fields" style="display: none;">
                            <div class="form-group">
                                <label>Checklist Items</label>
                                <div id="checklistItems">
                                    <div class="checklist-item-input">
                                        <input type="text" name="checklistItem[]" placeholder="Enter checklist item...">
                                        <button type="button" class="remove-item-btn" onclick="removeChecklistItem(this)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="add-item-btn" onclick="addChecklistItem()">
                                    <i class="bi bi-plus"></i> Add Item
                                </button>
                            </div>
                        </div>

                        <!-- Text field specific fields -->
                        <div id="textfieldFields" class="exercise-type-fields" style="display: none;">
                            <div class="form-group">
                                <label for="textfieldPlaceholder">Placeholder Text</label>
                                <input type="text" id="textfieldPlaceholder" name="textfieldPlaceholder"
                                       placeholder="e.g., Write your answer here...">
                            </div>

                            <div class="form-group">
                                <label for="minWords">Minimum Words (Optional)</label>
                                <input type="number" id="minWords" name="minWords" min="1" max="1000"
                                       placeholder="10" value="10">
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button type="button" class="btn-secondary" onclick="closeAddExerciseModal()">
                                Cancel
                            </button>
                            <button type="submit" class="btn-primary">
                                <i class="bi bi-plus-circle"></i> Create Exercise
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bootstrap JS -->
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

            <script>
                /**
                 * ASG Lessons System - JavaScript Limpio y Refactorizado
                 * Funciones organizadas y reutilizables
                 */

                // Production console cleanup - disable debug logs
                const isProduction = window.location.hostname !== 'localhost' &&
                    window.location.hostname !== '127.0.0.1' &&
                    !window.location.hostname.includes('dev');

                if (isProduction) {
                    // Keep console.error for important errors, disable debug logs
                    console.log = function() {};
                    console.warn = function() {};
                    console.info = function() {};
                    console.debug = function() {};
                }

                // Prevenir conflictos con otros scripts
                (function() {
                    'use strict';

                    // ===== CONFIGURACIÓN GLOBAL =====
                    const ASG_CONFIG = {
                        API_BASE: '<?php echo $site_url; ?>/wp-json/asg/v1',
                        COURSE_CODE: '<?php echo esc_js($course_code); ?>',
                        LESSON_ID: '<?php echo esc_js($lesson_id); ?>',
                        AUTO_LOAD_FIRST: <?php echo $auto_load_first ? 'true' : 'false'; ?>,
                        USER_ID: <?php echo get_current_user_id(); ?>,
                        USER_NAME: '<?php echo esc_js(wp_get_current_user()->display_name ?: wp_get_current_user()->user_login ?: "Estudiante"); ?>',
                        IS_ADMIN: <?php echo current_user_can('manage_options') ? 'true' : 'false'; ?>
                    };

                    // Make ASG_CONFIG globally accessible for exercise functions
                    window.ASG_CONFIG = ASG_CONFIG;

                    /**
                     * Configure admin navigation - hide progress elements and simplify navigation
                     */
                    function configureAdminNavigation() {
                        if (!ASG_CONFIG.IS_ADMIN) return;

                        // Hide Mark as Complete button
                        const btnMarkComplete = document.getElementById('btnMarkComplete');
                        if (btnMarkComplete) {
                            btnMarkComplete.style.display = 'none';
                        }

                        // Hide Certificate button (admins don't need certificates)
                        const btnViewCertificate = document.getElementById('btnViewCertificate');
                        if (btnViewCertificate) {
                            btnViewCertificate.style.display = 'none';
                        }

                        // Remove lock icons from lessons list (all lessons unlocked for admin)
                        const lockIcons = document.querySelectorAll('.lesson-lock-icon, .bi-lock, .bi-lock-fill');
                        lockIcons.forEach(icon => {
                            icon.style.display = 'none';
                        });

                        // Remove "locked" classes from lesson items
                        const lockedLessons = document.querySelectorAll('.lesson-item.locked, .lesson-locked');
                        lockedLessons.forEach(lesson => {
                            lesson.classList.remove('locked', 'lesson-locked');
                            lesson.style.opacity = '1';
                            lesson.style.pointerEvents = 'auto';
                        });
                    }

                    // Fallback: obtener parámetros de URL si no están definidos
                    if (!ASG_CONFIG.COURSE_CODE || !ASG_CONFIG.LESSON_ID) {
                        const urlParams = new URLSearchParams(window.location.search);

                        if (!ASG_CONFIG.COURSE_CODE) {
                            ASG_CONFIG.COURSE_CODE = urlParams.get('course') || '';
                        }

                        if (!ASG_CONFIG.LESSON_ID) {
                            ASG_CONFIG.LESSON_ID = urlParams.get('lesson') || '';
                        }

                        // Actualizar AUTO_LOAD_FIRST basado en parámetros
                        ASG_CONFIG.AUTO_LOAD_FIRST = !ASG_CONFIG.LESSON_ID && ASG_CONFIG.COURSE_CODE;
                    }



                    // ===== ESTADO GLOBAL =====
                    let asgState = {
                        currentCourse: null,
                        currentLesson: null,
                        courseProgress: null,
                        lessonsList: [],
                        isLoading: false
                    };

                    // Make asgState globally accessible for exercise functions
                    window.asgState = asgState;

                    // ===== FUNCIONES UTILITARIAS =====

                    /**
                     * Realizar petición API con manejo de errores
                     */
                    async function apiRequest(endpoint, options = {}) {
                        const url = `${ASG_CONFIG.API_BASE}${endpoint}`;

                        // Get nonce from meta tag
                        const nonce = document.querySelector('meta[name="wp-nonce"]')?.content;

                        const defaultOptions = {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        };

                        // Add nonce to headers if available and not already present
                        if (nonce && !options.headers?.['X-WP-Nonce']) {
                            defaultOptions.headers['X-WP-Nonce'] = nonce;
                        }

                        try {
                            const response = await fetch(url, {
                                ...defaultOptions,
                                ...options
                            });

                            if (!response.ok) {
                                // Log more details for debugging
                                console.error(`API Request failed: ${response.status} ${response.statusText}`, {
                                    url,
                                    method: options.method || 'GET',
                                    headers: {
                                        ...defaultOptions.headers,
                                        ...options.headers
                                    }
                                });
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }

                            return await response.json();
                        } catch (error) {
                            console.error('API Error:', error);
                            throw error;
                        }
                    }

                    /**
                     * Mostrar notificación al usuario
                     */
                    function showNotification(message, type = 'success', duration = 3000) {
                        const container = document.getElementById('notificationContainer');
                        const notification = document.createElement('div');

                        notification.className = `notification ${type}`;
                        notification.innerHTML = `
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                `;

                        container.appendChild(notification);

                        // Mostrar con animación
                        setTimeout(() => notification.classList.add('show'), 100);

                        // Ocultar después del tiempo especificado
                        setTimeout(() => {
                            notification.classList.remove('show');
                            setTimeout(() => container.removeChild(notification), 300);
                        }, duration);
                    }

                    // Make showNotification globally accessible for exercise functions
                    window.showNotification = showNotification;

                    /**
                     * Actualizar barra de progreso
                     */
                    function updateProgressBar(progressData) {
                        // Barra de progreso del header
                        const progressText = document.getElementById('progressText');
                        const progressFill = document.getElementById('progressFill');

                        if (progressText && progressData.progress_percentage !== undefined) {
                            progressText.textContent = `${progressData.progress_percentage}%`;
                        }

                        if (progressFill && progressData.progress_percentage !== undefined) {
                            progressFill.style.width = `${progressData.progress_percentage}%`;
                        }

                        // Barra de progreso del sidebar
                        const sidebarProgressText = document.getElementById('sidebarProgressText');
                        const sidebarProgressFill = document.getElementById('sidebarProgressFill');

                        if (sidebarProgressText && progressData.progress_percentage !== undefined) {
                            sidebarProgressText.textContent = `${progressData.progress_percentage}%`;
                        }

                        if (sidebarProgressFill && progressData.progress_percentage !== undefined) {
                            sidebarProgressFill.style.width = `${progressData.progress_percentage}%`;
                        }

                        // Show/hide certificate button based on completion
                        const certificateBtn = document.getElementById('btnViewCertificate');
                        if (certificateBtn && progressData.progress_percentage !== undefined) {
                            if (progressData.progress_percentage >= 100) {
                                certificateBtn.classList.add('show');
                                console.log('🏆 Certificate button shown - course completed!');
                            } else {
                                certificateBtn.classList.remove('show');
                            }
                        }
                    }

                    /**
                     * Obtener ID de lección actual desde URL
                     */
                    function getCurrentLessonId() {
                        const urlParams = new URLSearchParams(window.location.search);
                        return urlParams.get('lesson');
                    }

                    /**
                     * Check if a lesson is locked (Sequential Learning System)
                     */
                    function isLessonLocked(lessonId) {
                        // Admins never have locked lessons
                        if (ASG_CONFIG.IS_ADMIN) {
                            return false;
                        }

                        if (!asgState.courseProgress || !asgState.lessonsList) {

                            return false;
                        }

                        const completedLessons = asgState.courseProgress.completed_lessons_list || [];
                        const lessonIndex = asgState.lessonsList.findIndex(lesson => parseInt(lesson.id) === parseInt(lessonId));

                        // First lesson is always unlocked
                        if (lessonIndex === 0) {

                            return false;
                        }

                        // Check if previous lesson is completed
                        const previousLesson = asgState.lessonsList[lessonIndex - 1];
                        if (!previousLesson) {

                            return false;
                        }

                        const isPreviousCompleted = completedLessons.includes(parseInt(previousLesson.id));



                        return !isPreviousCompleted;
                    }

                    /**
                     * Show locked lesson message
                     */
                    function showLockedLessonMessage(lessonId) {
                        // Admins should never see locked lesson messages
                        if (ASG_CONFIG.IS_ADMIN) {
                            console.log('🔧 Admin user - skipping locked lesson message, loading lesson directly');
                            navigateToLesson(lessonId);
                            return;
                        }

                        const lessonIndex = asgState.lessonsList.findIndex(lesson => parseInt(lesson.id) === parseInt(lessonId));
                        const previousLesson = lessonIndex > 0 ? asgState.lessonsList[lessonIndex - 1] : null;

                        const message = `
                    <div class="locked-lesson-overlay">
                        <div class="locked-content">
                            <i class="bi bi-lock-fill text-warning" style="font-size: 3rem;"></i>
                            <h3>Lesson Locked</h3>
                            <p>You must complete the previous lesson to access this content.</p>
                            ${previousLesson ? `
                                <div class="previous-lesson-info">
                                    <p><strong>Complete first:</strong> ${previousLesson.title}</p>
                                    <button class="btn" style="background: #0C1B40; color: white;" onclick="navigateToLesson(${previousLesson.id})">
                                        <i class="bi bi-arrow-left me-2"></i>Go to Previous Lesson
                                    </button>
                                </div>
                            ` : `
                                <button class="btn" style="background: #0C1B40; color: white;" onclick="findNextUnlockedLesson()">
                                    <i class="bi bi-arrow-right me-2"></i>Find Available Lesson
                                </button>
                            `}
                        </div>
                    </div>
                `;

                        const contentArea = document.getElementById('lessonContentArea');
                        if (contentArea) {
                            contentArea.innerHTML = message;
                        }
                    }

                    /**
                     * Find the next available lesson (Sequential Learning)
                     */
                    function findNextUnlockedLesson() {
                        if (!asgState.courseProgress || !asgState.lessonsList) return null;

                        const completedLessons = asgState.courseProgress.completed_lessons_list || [];

                        // Find the first incomplete lesson
                        for (let i = 0; i < asgState.lessonsList.length; i++) {
                            const lesson = asgState.lessonsList[i];
                            const lessonId = parseInt(lesson.id);

                            // If this lesson is not completed
                            if (!completedLessons.includes(lessonId)) {
                                // Check if it's unlocked (first lesson or previous is completed)
                                if (i === 0 || completedLessons.includes(parseInt(asgState.lessonsList[i - 1].id))) {

                                    navigateToLesson(lessonId);
                                    return lessonId;
                                }
                            }
                        }

                        // All lessons completed
                        showNotification('🎉 Congratulations! You have completed all lessons!', 'success');
                        return null;
                    }

                    /**
                     * Navegar a una lección específica
                     */
                    function navigateToLesson(lessonId, replaceHistory = false) {
                        if (!lessonId) return;

                        // Verificar si la lección está bloqueada
                        if (isLessonLocked(lessonId)) {
                            showLockedLessonMessage(lessonId);
                            showNotification('Lesson is blocked, complete the previous lesson.', 'warning');
                            return;
                        }

                        const newUrl = `${window.location.pathname}?course=${ASG_CONFIG.COURSE_CODE}&lesson=${lessonId}`;

                        // Use replaceState for initial navigation to avoid creating unnecessary history entries
                        if (replaceHistory) {
                            window.history.replaceState({}, '', newUrl);
                        } else {
                            window.history.pushState({}, '', newUrl);
                        }

                        // Update the lesson ID in config
                        ASG_CONFIG.LESSON_ID = lessonId;

                        loadLessonContent(lessonId);
                    }

                    // ===== GESTIÓN DE PROGRESO =====

                    /**
                     * Load user progress from server
                     */
                    async function loadUserProgress() {
                        if (!ASG_CONFIG.COURSE_CODE) return;

                        try {
                            console.log('🔄 Loading user progress for:', ASG_CONFIG.COURSE_CODE);

                            const result = await apiRequest('/my-courses');
                            console.log('📊 Progress response:', result);

                            if (result.success && result.data.courses) {
                                const courseProgress = result.data.courses.find(course =>
                                    course.code_course === ASG_CONFIG.COURSE_CODE
                                );

                                if (courseProgress) {
                                    console.log('✅ Course progress found:', courseProgress);
                                    asgState.courseProgress = courseProgress;
                                    updateProgressBar(courseProgress);
                                    updateLessonsListUI(courseProgress);
                                    return courseProgress;
                                } else {
                                    console.log('⚠️ No progress found for course:', ASG_CONFIG.COURSE_CODE);
                                    return createInitialProgress();
                                }
                            } else {
                                console.error('❌ Error in progress response:', result);
                                throw new Error(result.message || 'Error getting progress');
                            }

                        } catch (error) {
                            console.error('❌ Error loading progress from server:', error);
                            console.log('🔄 Trying local storage fallback...');
                            return loadProgressFromLocalStorage();
                        }
                    }

                    /**
                     * Create initial progress structure
                     */
                    function createInitialProgress() {
                        const initialProgress = {
                            code_course: ASG_CONFIG.COURSE_CODE,
                            completed_lessons: 0,
                            total_lessons: asgState.lessonsList.length,
                            progress_percentage: 0,
                            completed_lessons_list: [],
                            unlocked_lessons_list: [parseInt(asgState.lessonsList[0]?.id)] // Only first lesson unlocked
                        };
                        asgState.courseProgress = initialProgress;
                        updateProgressBar(initialProgress);
                        updateLessonsListUI(initialProgress);
                        return initialProgress;
                    }

                    /**
                     * Load progress from local storage as fallback
                     */
                    function loadProgressFromLocalStorage() {
                        const storageKey = `asg_progress_${ASG_CONFIG.COURSE_CODE}`;
                        const localProgress = JSON.parse(localStorage.getItem(storageKey) || '{}');

                        const fallbackProgress = {
                            code_course: ASG_CONFIG.COURSE_CODE,
                            completed_lessons: localProgress.completed_lessons?.length || 0,
                            total_lessons: asgState.lessonsList.length,
                            progress_percentage: 0,
                            completed_lessons_list: localProgress.completed_lessons || [],
                            unlocked_lessons_list: [parseInt(asgState.lessonsList[0]?.id)]
                        };

                        // Calculate progress percentage
                        if (fallbackProgress.total_lessons > 0) {
                            fallbackProgress.progress_percentage = Math.round(
                                (fallbackProgress.completed_lessons / fallbackProgress.total_lessons) * 100
                            );
                        }

                        console.log('💾 Using local storage progress:', fallbackProgress);
                        asgState.courseProgress = fallbackProgress;
                        updateProgressBar(fallbackProgress);
                        updateLessonsListUI(fallbackProgress);
                        return fallbackProgress;
                    }

                    /**
                     * Mark lesson as completed con verificación robusta
                     */
                    async function markLessonComplete(lessonId) {
                        if (!lessonId) {
                            lessonId = getCurrentLessonId();
                        }

                        if (!lessonId) {
                            showNotification('Could not identify current lesson', 'error');
                            return;
                        }

                        console.log('🎯 Marking lesson as complete:', lessonId);

                        // VERIFICACIÓN ROBUSTA: Verificar acceso antes de marcar como completada
                        const hasAccess = await verifyAccessBeforeLoad();
                        if (!hasAccess) {
                            console.log('🚫 Access denied - cannot mark lesson as complete');
                            showNotification('Access denied. Please purchase the course to continue.', 'error');
                            return;
                        }

                        try {
                            // Try the primary API endpoint first
                            const url = `/lesson/${lessonId}/complete`;
                            console.log('📤 Sending POST to:', ASG_CONFIG.API_BASE + url);

                            const result = await apiRequest(url, {
                                method: 'POST',
                                headers: {
                                    'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || '',
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    course: ASG_CONFIG.COURSE_CODE,
                                    lesson_id: parseInt(lessonId)
                                })
                            });

                            if (result.success) {
                                handleLessonCompletionSuccess(lessonId);
                                return;
                            } else {
                                throw new Error(result.message || 'Server error');
                            }

                        } catch (error) {
                            console.error('❌ Primary endpoint failed:', error);

                            // Try fallback endpoint without nonce
                            try {
                                console.log('🔄 Trying fallback endpoint...');
                                const fallbackUrl = `/lesson/${lessonId}/complete-no-nonce`;

                                const fallbackResult = await apiRequest(fallbackUrl, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({
                                        course: ASG_CONFIG.COURSE_CODE,
                                        lesson_id: parseInt(lessonId)
                                    })
                                });

                                if (fallbackResult.success) {
                                    handleLessonCompletionSuccess(lessonId);
                                    return;
                                } else {
                                    throw new Error(fallbackResult.message || 'Fallback error');
                                }

                            } catch (fallbackError) {
                                console.error('❌ Fallback endpoint failed:', fallbackError);

                                // Final fallback: Local storage completion
                                console.log('🔄 Using local storage fallback...');
                                handleLocalLessonCompletion(lessonId);
                            }
                        }
                    }

                    /**
                     * Handle successful lesson completion
                     */
                    function handleLessonCompletionSuccess(lessonId) {
                        showNotification('🎉 Lesson completed successfully!', 'success');

                        // Update local state
                        if (!asgState.courseProgress) {
                            asgState.courseProgress = {
                                completed_lessons_list: [],
                                progress_percentage: 0
                            };
                        }

                        // Add lesson to completed list if not already there
                        const completedLessons = asgState.courseProgress.completed_lessons_list || [];
                        if (!completedLessons.includes(parseInt(lessonId))) {
                            completedLessons.push(parseInt(lessonId));
                            asgState.courseProgress.completed_lessons_list = completedLessons;

                            // Update progress percentage
                            const totalLessons = asgState.lessonsList.length;
                            asgState.courseProgress.progress_percentage = Math.round((completedLessons.length / totalLessons) * 100);
                        }

                        // Update UI
                        updateLessonCompleteButton(lessonId, true);
                        updateProgressBar(asgState.courseProgress);

                        // Scroll to lesson title immediately after completion
                        setTimeout(() => {
                            scrollToLessonTitle();
                        }, 300); // Small delay to let UI updates finish

                        // Check if course is completed (100%)
                        if (asgState.courseProgress.progress_percentage >= 100) {
                            console.log('🎉 COURSE COMPLETED! Showing certificate modal...');
                            setTimeout(() => {
                                showCourseCompletionModal();
                            }, 1000); // Show after 1 second to let progress bar animation finish
                        }

                        // Title next lesson modal
                        function showNextLessonOverlay(text = 'next lesson', visibleDuration = 3000) {
                            const overlay = document.createElement('div');
                            overlay.className = 'overlay-next-lesson';
                            overlay.textContent = text;
                            document.body.appendChild(overlay);

                            // forzar reflow + fade-in
                            requestAnimationFrame(() => {
                                overlay.classList.add('show');
                            });

                            // tras visibleDuration milisegundos, iniciar fade-out
                            setTimeout(() => {
                                overlay.classList.add('hide');
                                // al acabar la transición, quitar del DOM
                                overlay.addEventListener('transitionend', () => overlay.remove(), {
                                    once: true
                                });
                            }, visibleDuration);
                        }


                        // Force refresh all lesson states and reload from server
                        setTimeout(async () => {
                            console.log('🔄 Refreshing progress from server after completion...');

                            // Reload progress from server to ensure sync
                            await loadUserProgress();

                            // Refresh UI with updated data
                            refreshLessonStates();

                            const nextLesson = getNextLesson(lessonId);
                            if (nextLesson && asgState.courseProgress.progress_percentage < 100) {
                                // Only auto-navigate if course is not completed
                                // AUTO-NAVIGATE to next lesson after 2 seconds
                                setTimeout(() => {
                                    console.log('🚀 Auto-navigating to next lesson:', nextLesson.title);
                                    navigateToLesson(nextLesson.id);

                                    // Scroll to lesson title after navigation
                                    setTimeout(() => {
                                        window.scrollTo({
                                            top: 0,
                                            behavior: 'smooth'
                                        });
                                    }, 500); // Wait for content to load

                                    // Lesson modal next lesson
                                    setTimeout(() => {
                                        showNextLessonOverlay('next lesson', 3000);
                                    }, 1500);

                                }, 1000);
                            }

                        }, 500);
                    }

                    /**
                     * Handle lesson completion using local storage as fallback
                     */
                    function handleLocalLessonCompletion(lessonId) {
                        console.log('💾 Using local storage for lesson completion');

                        // Get or create local progress
                        const storageKey = `asg_progress_${ASG_CONFIG.COURSE_CODE}`;
                        let localProgress = JSON.parse(localStorage.getItem(storageKey) || '{}');

                        if (!localProgress.completed_lessons) {
                            localProgress.completed_lessons = [];
                        }

                        // Add lesson if not already completed
                        if (!localProgress.completed_lessons.includes(parseInt(lessonId))) {
                            localProgress.completed_lessons.push(parseInt(lessonId));
                            localStorage.setItem(storageKey, JSON.stringify(localProgress));
                        }

                        // Update global state
                        if (!asgState.courseProgress) {
                            asgState.courseProgress = {
                                completed_lessons_list: [],
                                progress_percentage: 0
                            };
                        }

                        asgState.courseProgress.completed_lessons_list = localProgress.completed_lessons;
                        const totalLessons = asgState.lessonsList.length;
                        asgState.courseProgress.progress_percentage = Math.round((localProgress.completed_lessons.length / totalLessons) * 100);


                        handleLessonCompletionSuccess(lessonId);
                    }

                    /**
                     * Get next lesson in sequence
                     */
                    function getNextLesson(currentLessonId) {
                        if (!asgState.lessonsList) return null;

                        const currentIndex = asgState.lessonsList.findIndex(lesson =>
                            parseInt(lesson.id) === parseInt(currentLessonId)
                        );

                        if (currentIndex >= 0 && currentIndex < asgState.lessonsList.length - 1) {
                            return asgState.lessonsList[currentIndex + 1];
                        }

                        return null;
                    }

                    /**
                     * Show course completion celebration modal with certificate
                     */
                    function showCourseCompletionModal() {
                        // Create modal HTML
                        const modalHTML = `
                    <div id="courseCompletionModal" class="completion-modal-overlay">
                        <div class="completion-modal-content">
                            <div class="completion-celebration">
                                <div class="confetti-container">
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                </div>

                                <div class="completion-header">
                                    <div class="completion-icon">🎉</div>
                                    <h2>Congratulations!</h2>
                                    <p>You have successfully completed the course</p>
                                </div>

                                <div class="certificate-container">
                                    <div class="certificate">
                                        <div class="certificate-border">
                                            <div class="certificate-content">
                                                <div class="certificate-header">
                                                    <h3>CERTIFICATE OF COMPLETION</h3>
                                                    <div class="certificate-logo">🏆</div>
                                                </div>

                                                <div class="certificate-body">
                                                    <p class="certificate-text">This is to certify that</p>
                                                    <h4 class="student-name" id="certificateStudentName">Student</h4>
                                                    <p class="certificate-text">has successfully completed the course</p>
                                                    <h4 class="course-name" id="certificateCourseName">Course Name</h4>
                                                    <p class="completion-date">Date of completion: <span id="certificateDate"></span></p>
                                                </div>

                                                <div class="certificate-footer">
                                                    <div class="signature-line">
                                                        <div class="signature">Ability Seminars Group</div>
                                                        <div class="signature-title">Certifying Institution</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="completion-actions">
                                    <button class="btn-download-certificate" onclick="downloadCertificate()">
                                        📄 Download Certificate
                                    </button>
                                    <button class="btn-share-achievement" onclick="shareAchievement()">
                                        🔗 Share Achievement
                                    </button>
                                    <button class="btn-close-modal" onclick="closeCourseCompletionModal()">
                                        Continue
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                        // Add modal to page
                        document.body.insertAdjacentHTML('beforeend', modalHTML);

                        // Populate certificate data
                        populateCertificateData();

                        // Show modal with animation
                        setTimeout(() => {
                            document.getElementById('courseCompletionModal').classList.add('show');
                        }, 100);
                    }

                    /**
                     * Populate certificate with user and course data
                     */
                    function populateCertificateData() {
                        // Get current user info (you might need to adjust this based on your user system)
                        const userName = ASG_CONFIG.USER_NAME || 'Student';
                        const courseName = asgState.currentCourse?.name_course || 'Completed Course';
                        const currentDate = new Date().toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        });

                        // Populate certificate fields
                        document.getElementById('certificateStudentName').textContent = userName;
                        document.getElementById('certificateCourseName').textContent = courseName;
                        document.getElementById('certificateDate').textContent = currentDate;
                    }

                    /**
                     * Download certificate as image
                     */
                    function downloadCertificate() {
                        const certificate = document.querySelector('.certificate');

                        // Use html2canvas to convert certificate to image
                        if (typeof html2canvas !== 'undefined') {
                            html2canvas(certificate, {
                                backgroundColor: '#ffffff',
                                scale: 2,
                                useCORS: true
                            }).then(canvas => {
                                const link = document.createElement('a');
                                link.download = `certificado-${asgState.currentCourse?.name_course || 'curso'}.png`;
                                link.href = canvas.toDataURL();
                                link.click();
                            });
                        } else {
                            // Fallback: open certificate in new window for manual save
                            const printWindow = window.open('', '_blank');
                            printWindow.document.write(`
                        <html>
                            <head>
                                <title>Certificado de Finalización</title>
                                <style>
                                    body { margin: 0; padding: 20px; font-family: 'Outfit', sans-serif; }
                                    .certificate { max-width: 800px; margin: 0 auto; }
                                </style>
                            </head>
                            <body>
                                ${certificate.outerHTML}
                            </body>
                        </html>
                    `);
                            printWindow.document.close();
                        }
                    }

                    /**
                     * Share achievement on social media
                     */
                    function shareAchievement() {
                        const courseName = asgState.currentCourse?.name_course || 'a course';
                        const shareText = `I just completed the course "${courseName}" at Ability Seminars Group! 🎉🏆`;
                        const shareUrl = window.location.href;

                        if (navigator.share) {
                            // Use native sharing if available
                            navigator.share({
                                title: 'Certificate of Completion',
                                text: shareText,
                                url: shareUrl
                            });
                        } else {
                            // Fallback: copy to clipboard
                            navigator.clipboard.writeText(`${shareText} ${shareUrl}`).then(() => {
                                showNotification('Text copied to clipboard! Share it on your social media.', 'success');
                            });
                        }
                    }

                    /**
                     * Close course completion modal
                     */
                    function closeCourseCompletionModal() {
                        const modal = document.getElementById('courseCompletionModal');
                        if (modal) {
                            modal.classList.remove('show');
                            setTimeout(() => {
                                modal.remove();
                            }, 300);
                        }
                    }

                    /**
                     * Scroll to lesson title for better user experience
                     */
                    function scrollToLessonTitle() {
                        const lessonTitle = document.querySelector('.lesson-title-main');
                        if (lessonTitle) {
                            lessonTitle.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start',
                                inline: 'nearest'
                            });
                            console.log('📍 Scrolled to lesson title');
                        } else {
                            // Fallback: scroll to lesson header if lesson-title-main not found
                            const lessonHeader = document.querySelector('.lesson-header');
                            if (lessonHeader) {
                                lessonHeader.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start',
                                    inline: 'nearest'
                                });
                                console.log('📍 Scrolled to lesson header (fallback)');
                            }
                        }
                    }

                    /**
                     * Load comments from backend and inject them into #commentsList
                     */






                    /**
                     * Get previous lesson in sequence
                     */
                    function getPreviousLesson(currentLessonId) {
                        if (!asgState.lessonsList) return null;

                        const currentIndex = asgState.lessonsList.findIndex(lesson =>
                            parseInt(lesson.id) === parseInt(currentLessonId)
                        );

                        if (currentIndex > 0) {
                            return asgState.lessonsList[currentIndex - 1];
                        }

                        return null;
                    }



                    /**
                     * Force refresh all lesson states after completion
                     */
                    function refreshLessonStates() {

                        // Force re-render of lessons list
                        if (asgState.courseProgress) {
                            updateLessonsListUI(asgState.courseProgress);
                        }

                        // Update navigation buttons
                        updateNavigationButtons();

                        // Update progress bar
                        if (asgState.courseProgress) {
                            updateProgressBar(asgState.courseProgress);
                        }

                        // Update complete button for current lesson
                        const currentLessonId = getCurrentLessonId();
                        if (currentLessonId && asgState.courseProgress) {
                            const isCompleted = asgState.courseProgress.completed_lessons_list &&
                                asgState.courseProgress.completed_lessons_list.includes(parseInt(currentLessonId));
                            updateLessonCompleteButton(currentLessonId, isCompleted);
                        }

                    }

                    /**
                     * ===== VERIFICACIÓN ROBUSTA DE ENROLLMENT (JavaScript) =====
                     * Capa adicional de seguridad para verificar enrollment
                     */
                    async function checkEnrollmentRobust() {
                        if (!ASG_CONFIG.COURSE_CODE) {
                            console.warn('🚫 No course code provided for enrollment check');
                            return false;
                        }

                        // Administradores siempre tienen acceso
                        if (ASG_CONFIG.IS_ADMIN) {
                            console.log('✅ Admin user - enrollment check bypassed');
                            return true;
                        }

                        try {
                            console.log('🔍 Checking enrollment for course:', ASG_CONFIG.COURSE_CODE);

                            const result = await apiRequest(`/check-enrollment?course=${ASG_CONFIG.COURSE_CODE}`);
                            console.log('📊 Enrollment check result:', result);

                            if (result.success && result.enrolled) {
                                console.log('✅ User is enrolled in course');
                                return true;
                            } else {
                                console.log('❌ User is NOT enrolled in course');
                                return false;
                            }

                        } catch (error) {
                            console.error('🚨 Error checking enrollment:', error);
                            // En caso de error, asumir que no está inscrito por seguridad
                            return false;
                        }
                    }

                    /**
                     * Verificación de acceso antes de cargar contenido
                     */
                    async function verifyAccessBeforeLoad() {
                        console.log('🔐 Verifying access before loading content...');

                        // Verificar si el usuario está logueado
                        if (!ASG_CONFIG.USER_ID) {
                            console.log('🚫 User not logged in - blocking access');
                            logSecurityEvent('ACCESS_DENIED_NOT_LOGGED_IN', {
                                courseCode: ASG_CONFIG.COURSE_CODE,
                                attemptedAction: 'access_course_content'
                            });
                            blockAccessAndRedirectWithLogging('login', 'User not logged in');
                            return false;
                        }

                        // Verificar si es administrador - acceso total
                        if (ASG_CONFIG.IS_ADMIN) {
                            console.log('✅ Admin user detected - full access granted');
                            logSecurityEvent('ACCESS_GRANTED_ADMIN', {
                                courseCode: ASG_CONFIG.COURSE_CODE,
                                userId: ASG_CONFIG.USER_ID,
                                verificationLevel: 'ADMIN'
                            });
                            return true;
                        }

                        // Verificar enrollment para usuarios regulares
                        const isEnrolled = await checkEnrollmentRobust();
                        if (!isEnrolled) {
                            console.log('🚫 User not enrolled - blocking access');
                            logSecurityEvent('ACCESS_DENIED_NOT_ENROLLED', {
                                courseCode: ASG_CONFIG.COURSE_CODE,
                                userId: ASG_CONFIG.USER_ID,
                                attemptedAction: 'access_course_content'
                            });
                            blockAccessAndRedirectWithLogging('purchase', 'User not enrolled in course');
                            return false;
                        }

                        console.log('✅ Access verified - user can proceed');
                        logSecurityEvent('ACCESS_GRANTED', {
                            courseCode: ASG_CONFIG.COURSE_CODE,
                            userId: ASG_CONFIG.USER_ID,
                            verificationLevel: 'ROBUST'
                        });
                        return true;
                    }

                    /**
                     * Bloquear acceso y redirigir según el tipo de problema
                     */
                    function blockAccessAndRedirect(type) {
                        const contentArea = document.getElementById('lessonContentArea');
                        let redirectUrl = '';
                        let message = '';
                        let buttonText = '';
                        let buttonIcon = '';

                        if (type === 'login') {
                            redirectUrl = `/wp-login.php?redirect_to=${encodeURIComponent(window.location.href)}`;
                            message = 'You need to sign in to access this course.';
                            buttonText = 'Sign In';
                            buttonIcon = 'box-arrow-in-right';
                        } else if (type === 'purchase') {
                            redirectUrl = `/payment/?course=${encodeURIComponent(ASG_CONFIG.COURSE_CODE)}&price=10`;
                            message = 'You need to purchase this course to access the lessons.';
                            buttonText = 'Purchase Course';
                            buttonIcon = 'cart-plus';
                        }

                        if (contentArea) {
                            contentArea.innerHTML = `
                        <div class="access-blocked-overlay" style="
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            min-height: 400px;
                            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                            border-radius: 15px;
                            padding: 3rem;
                            text-align: center;
                        ">
                            <i class="bi bi-shield-x" style="font-size: 4rem; color: #dc3545; margin-bottom: 1.5rem;"></i>
                            <h3 style="color: var(--primary-blue); margin-bottom: 1rem;">Access Blocked</h3>
                            <p style="color: var(--text-light); font-size: 1.1rem; margin-bottom: 2rem;">${message}</p>
                            <button onclick="window.location.href='${redirectUrl}'" class="btn btn-lg" style="
                                background: var(--primary-blue);
                                color: white;
                                padding: 0.75rem 2rem;
                                border-radius: 25px;
                                border: none;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">
                                <i class="bi bi-${buttonIcon} me-2"></i>${buttonText}
                            </button>
                            <small class="text-muted mt-3">
                                <i class="bi bi-shield-check me-1"></i>
                                Security verification by ASG Learning Platform
                            </small>
                        </div>
                    `;
                        }

                        // También bloquear navegación
                        const navButtons = document.querySelectorAll('.nav-button, .completion-button');
                        navButtons.forEach(button => {
                            button.disabled = true;
                            button.style.opacity = '0.5';
                            button.style.cursor = 'not-allowed';
                        });

                        // Mostrar notificación
                        showNotification(message, 'error', 5000);
                    }

                    // Mantener función original para compatibilidad
                    async function checkEnrollment() {
                        return await checkEnrollmentRobust();
                    }

                    /**
                     * ===== SISTEMA DE LOGGING DE SEGURIDAD =====
                     */
                    function logSecurityEvent(eventType, details) {
                        const timestamp = new Date().toISOString();
                        const logEntry = {
                            timestamp,
                            eventType,
                            courseCode: ASG_CONFIG.COURSE_CODE,
                            userId: ASG_CONFIG.USER_ID,
                            userAgent: navigator.userAgent,
                            url: window.location.href,
                            details
                        };

                        console.log(`🔒 SECURITY LOG [${eventType}]:`, logEntry);

                        // Enviar log al servidor (opcional)
                        try {
                            fetch(`${ASG_CONFIG.API_BASE}/security-log`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                                },
                                body: JSON.stringify(logEntry)
                            }).catch(error => {
                                console.warn('Failed to send security log to server:', error);
                            });
                        } catch (error) {
                            console.warn('Security logging error:', error);
                        }
                    }

                    /**
                     * Función mejorada de bloqueo con logging
                     */
                    function blockAccessAndRedirectWithLogging(type, reason) {
                        // Log del evento de seguridad
                        logSecurityEvent('ACCESS_BLOCKED', {
                            blockType: type,
                            reason: reason,
                            timestamp: new Date().toISOString()
                        });

                        // Llamar a la función original de bloqueo
                        blockAccessAndRedirect(type);
                    }

                    // ===== GESTIÓN DE LECCIONES =====

                    /**
                     * Cargar datos del curso y lecciones con verificación robusta
                     */
                    async function loadCourseData() {
                        console.log('🚀 loadCourseData() called');

                        if (!ASG_CONFIG.COURSE_CODE) {
                            console.error('🚫 No course code provided');
                            return;
                        }

                        try {
                            asgState.isLoading = true;
                            console.log('🔄 Loading course data for:', ASG_CONFIG.COURSE_CODE);

                            // VERIFICACIÓN ROBUSTA: Verificar acceso antes de cargar datos
                            const hasAccess = await verifyAccessBeforeLoad();
                            if (!hasAccess) {
                                console.log('🚫 Access denied - stopping course data load');
                                asgState.isLoading = false;
                                return;
                            }

                            // Usar el endpoint correcto que existe
                            const result = await apiRequest(`/courses/api/${ASG_CONFIG.COURSE_CODE}`);
                            console.log('📊 Course data response:', result);

                            if (result.success && result.data) {
                                // El endpoint devuelve un curso individual
                                const courseData = result.data;

                                asgState.currentCourse = courseData;
                                asgState.lessonsList = extractLessonsFromCourse(courseData);

                                // Actualizar título del curso en el header
                                const courseTitle = document.getElementById('courseTitle');
                                if (courseTitle) {
                                    const title = courseData.title || courseData.name_course || courseData.course_title || 'Curso';
                                    courseTitle.textContent = title;
                                    console.log('📚 Course title updated:', title);
                                }

                                renderLessonsList();

                                // VERIFICACIÓN ROBUSTA: Doble verificación de enrollment
                                const isEnrolled = await checkEnrollmentRobust();
                                if (!isEnrolled) {
                                    console.log('🚨 SECURITY ALERT: User bypassed initial verification');
                                    blockAccessAndRedirect('purchase');
                                    return;
                                }

                                // Cargar progreso del usuario
                                await loadUserProgress();

                                // Cargar lección específica o primera lección
                                if (ASG_CONFIG.LESSON_ID) {
                                    loadLessonContent(ASG_CONFIG.LESSON_ID);
                                } else if (ASG_CONFIG.AUTO_LOAD_FIRST && asgState.lessonsList.length > 0) {
                                    // Navigate to first lesson (this will update URL and load content)
                                    // Use replaceHistory=true to replace the current URL instead of adding to history
                                    navigateToLesson(asgState.lessonsList[0].id, true);
                                }

                            } else {

                                showError('No se pudo cargar el curso');
                            }
                        } catch (error) {
                            showError('Error cargando datos del curso: ' + error.message);
                        } finally {
                            asgState.isLoading = false;
                        }
                    }

                    /**
                     * Extraer lista de lecciones desde datos del curso
                     */
                    function extractLessonsFromCourse(courseData) {
                        const lessons = [];


                        if (courseData.modules && Array.isArray(courseData.modules)) {
                            courseData.modules.forEach((module, moduleIndex) => {
                                console.log(`Procesando módulo ${moduleIndex}:`, module);

                                if (module.lessons && Array.isArray(module.lessons)) {
                                    module.lessons.forEach((lesson, lessonIndex) => {
                                        console.log(`  Procesando lección ${lessonIndex}:`, lesson);

                                        // Debug: Log raw lesson data to see what's coming from API
                                        if (lessonIndex === 0) {
                                            console.log('🔍 Raw lesson data from API:', lesson);
                                        }

                                        lessons.push({
                                            id: lesson.id_lesson || lesson.id,
                                            id_lesson: lesson.id_lesson || lesson.id,
                                            module_id: lesson.module_id || module.id_modules || module.id_module || module.id,
                                            title: lesson.title_lesson || lesson.title || `Lección ${lessonIndex + 1}`,
                                            type: lesson.lesson_type || lesson.type_lesson || lesson.type || 'video',
                                            content: lesson.content_lesson || lesson.content || '',
                                            video_url: lesson.video_url || lesson.videoUrl || '',
                                            cover_img: lesson.cover_img || '',
                                            lesson_template: lesson.lesson_template || 'basic',
                                            lesson_images: lesson.lesson_images || null,
                                            lesson_sections: lesson.lesson_sections || null,
                                            module_title: module.title_module || module.title || `Módulo ${moduleIndex + 1}`,
                                            module_cover_img: module.cover_img || '',
                                            is_preview: lesson.is_preview === '1' || lesson.is_preview === 1 || lesson.isPreview === true
                                        });
                                    });
                                } else {

                                }
                            });
                        } else {

                        }


                        return lessons;
                    }

                    /**
                     * Cargar contenido de una lección específica con verificación robusta
                     */
                    async function loadLessonContent(lessonId) {
                        console.log('🔄 Loading lesson content for ID:', lessonId);

                        // VERIFICACIÓN ROBUSTA: Verificar acceso antes de cargar lección
                        const hasAccess = await verifyAccessBeforeLoad();
                        if (!hasAccess) {
                            console.log('🚫 Access denied - stopping lesson content load');
                            return;
                        }

                        const lesson = asgState.lessonsList.find(l => l.id == lessonId);

                        if (!lesson) {
                            console.error('❌ Lesson not found:', lessonId);
                            showError('Lesson not found');
                            return;
                        }

                        try {
                            // Normalizar tipo de lección antes de renderizar
                            if (!lesson.type && !lesson.type_lesson && !lesson.lesson_type) {
                                // Auto-detectar tipo basado en contenido
                                if (lesson.content) {
                                    try {
                                        const parsedContent = JSON.parse(lesson.content);
                                        if (parsedContent.questions && Array.isArray(parsedContent.questions)) {
                                            lesson.type = 'quiz';
                                        } else {
                                            lesson.type = lesson.video_url ? 'video' : 'text';
                                        }
                                    } catch (e) {
                                        // No es JSON válido, determinar por video_url
                                        lesson.type = lesson.video_url ? 'video' : 'text';
                                    }
                                } else if (lesson.video_url) {
                                    lesson.type = 'video';
                                } else {
                                    // Sin contenido ni video, asumir text
                                    lesson.type = 'text';
                                }
                            }

                            asgState.currentLesson = lesson;
                            updateLessonsListActive(lessonId);
                            renderLessonContent(lesson);

                            // Initialize exercises for this lesson
                            setTimeout(() => initializeExercisesForLesson(), 100);
                        } catch (error) {
                            console.error('Error cargando contenido de lección:', error);
                            showError('Error cargando contenido de la lección');
                        }
                    }

                    // ===== GESTIÓN DE UI =====

                    /**
                     * Renderizar estructura de módulos y lecciones en el sidebar
                     */
                    function renderLessonsList() {
                        const container = document.getElementById('modulesList');

                        if (!container) {
                            return;
                        }

                        if (!asgState.currentCourse || !asgState.currentCourse.modules) {
                            container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-book text-white" style="font-size: 2rem;"></i>
                            <p class="text-white mt-2">No modules available</p>
                            <button class="btn btn-sm btn-outline-primary" onclick="loadCourseData()">
                                <i class="bi bi-arrow-clockwise me-1"></i>Reload
                            </button>
                        </div>
                    `;
                            return;
                        }

                        // Actualizar título del curso
                        const courseTitle = document.getElementById('courseTitle');
                        if (courseTitle) {
                            const title = asgState.currentCourse.title || asgState.currentCourse.name_course || asgState.currentCourse.course_title || 'Curso';
                            courseTitle.innerHTML = `
                        <i class="bi bi-mortarboard me-2"></i>
                        ${title}
                    `;
                        }

                        // Renderizar módulos
                        const modulesHtml = asgState.currentCourse.modules.map((module, moduleIndex) => {
                            const moduleId = `module-${moduleIndex}`;
                            const lessonsHtml = module.lessons ? module.lessons.map(lesson => {
                                const isActive = lesson.id_lesson == ASG_CONFIG.LESSON_ID;
                                const lessonType = lesson.type_lesson || lesson.lesson_type || 'video';
                                const icon = lessonType === 'video' ? 'play-circle' : 'question-circle';
                                const isPreview = lesson.is_preview === '1' || lesson.is_preview === 1;

                                return `
                            <div class="lesson-item ${isActive ? 'active' : ''} ${isPreview ? 'preview-lesson' : 'unlocked-lesson'}"
                                 onclick="navigateToLesson(${lesson.id_lesson})"
                                 data-lesson-id="${lesson.id_lesson}">
                                <div class="lesson-icon me-3">
                                    <i class="bi bi-${icon}"></i>
                                </div>
                                <div class="lesson-title flex-grow-1">${lesson.title_lesson}</div>
                                ${isPreview ? '<span class="badge bg-warning ms-2">Preview</span>' : ''}
                                <div class="lesson-status ms-2">
                                    <i class="bi bi-circle text-white"></i>
                                </div>
                            </div>
                        `;
                            }).join('') : '<p class="text-white p-3">You dont have any lessons in this module</p>';

                            return `
                        <div class="module-item">
                            <div class="module-header" onclick="toggleModule('${moduleId}')">
                                <h6 class="module-title">${module.title_module || `Module ${moduleIndex + 1}`}</h6>
                                <i class="bi bi-chevron-right module-chevron" id="chevron-${moduleId}"></i>
                            </div>
                            <div class="module-lessons" id="${moduleId}">
                                <div class="lessons-list">
                                    ${lessonsHtml}
                                </div>
                            </div>
                        </div>
                    `;
                        }).join('');

                        // Agregar botón de nuevo módulo si es admin
                        let newModuleButton = '';
                        newModuleButton = `
                            <div class="module-item new-module-item">
                                <div class="new-module-button" onclick="showAddModuleModal()">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    <span>Add New Module (Debug)</span>
                                </div>
                            </div>
                        `;

                        container.innerHTML = modulesHtml + newModuleButton;

                        // Expandir el primer módulo por defecto
                        setTimeout(() => {
                            const firstModule = document.getElementById('module-0');
                            const firstChevron = document.getElementById('chevron-module-0');
                            if (firstModule && firstChevron) {
                                firstModule.classList.add('show');
                                firstChevron.classList.add('rotated');
                            }
                        }, 100);


                    }

                    /**
                     * Actualizar estado activo en lista de lecciones
                     */
                    function updateLessonsListActive(activeLessonId) {
                        const items = document.querySelectorAll('.lesson-item');
                        items.forEach(item => {
                            item.classList.remove('active');
                            if (item.dataset.lessonId == activeLessonId) {
                                item.classList.add('active');
                            }
                        });
                    }

                    /**
                     * Update lessons list UI with progress and sequential locking
                     */
                    function updateLessonsListUI(progressData) {
                        if (!progressData) return;

                        const completedLessons = progressData.completed_lessons_list || [];
                        const allLessons = progressData.all_lessons_list || [];

                        console.log('🎨 Updating lessons UI (Sequential Learning):', {
                            completed: completedLessons,
                            total: allLessons.length
                        });

                        // Check if modules list container exists
                        const modulesList = document.getElementById('modulesList');
                        if (!modulesList) {
                            console.log('ℹ️ Modules list container not found - UI update skipped');
                            return;
                        }

                        // If no lesson data, get from current structure
                        if (allLessons.length === 0 && asgState.currentCourse && asgState.currentCourse.modules) {
                            asgState.currentCourse.modules.forEach(module => {
                                if (module.lessons) {
                                    module.lessons.forEach(lesson => {
                                        allLessons.push(parseInt(lesson.id_lesson));
                                    });
                                }
                            });
                        }

                        // Use lessonsList for sequential order
                        const orderedLessons = asgState.lessonsList || [];

                        // Apply states to all lessons (Sequential Learning Logic)
                        orderedLessons.forEach((lesson, index) => {
                            const lessonId = parseInt(lesson.id);
                            const lessonElement = document.querySelector(`[data-lesson-id="${lessonId}"]`);
                            if (!lessonElement) {
                                console.log(`⚠️ Lesson element not found for ID: ${lessonId}`);
                                return;
                            }

                            // Clear previous classes
                            lessonElement.classList.remove('completed', 'locked-lesson', 'unlocked-lesson');

                            // Remove previous badges
                            const existingBadge = lessonElement.querySelector('.lesson-badge');
                            if (existingBadge) existingBadge.remove();

                            const isCompleted = completedLessons.includes(lessonId);
                            const isFirstLesson = index === 0;
                            const isPreviousCompleted = index === 0 || completedLessons.includes(parseInt(orderedLessons[index - 1].id));
                            const isUnlocked = isFirstLesson || isPreviousCompleted;

                            // Apply lesson state
                            if (isCompleted) {
                                // ✅ COMPLETED LESSON
                                lessonElement.classList.add('completed');
                                const statusIcon = lessonElement.querySelector('.lesson-status i');
                                if (statusIcon) {
                                    statusIcon.className = 'bi bi-check-circle-fill text-success';
                                }

                                // Visual completed style
                                lessonElement.style.opacity = '0.8';
                                lessonElement.style.textDecoration = 'line-through';

                                // Add completed badge
                                const badge = document.createElement('span');
                                badge.className = 'lesson-badge badge bg-success ms-2';
                                badge.innerHTML = '<i class="bi bi-check"></i> Completed';
                                lessonElement.appendChild(badge);

                            } else if (isUnlocked) {
                                // 🔓 UNLOCKED LESSON
                                lessonElement.classList.add('unlocked-lesson');
                                const statusIcon = lessonElement.querySelector('.lesson-status i');
                                if (statusIcon) {
                                    statusIcon.className = 'bi bi-play-circle text-primary';
                                }

                                // Reset styles
                                lessonElement.style.opacity = '1';
                                lessonElement.style.textDecoration = 'none';
                                lessonElement.style.cursor = 'pointer';

                                // Add available badge
                                const badge = document.createElement('span');
                                badge.className = 'lesson-badge badge bg-primary ms-2';
                                badge.innerHTML = '<i class="bi bi-play"></i> Available';
                                lessonElement.appendChild(badge);

                            } else {
                                // 🔒 LOCKED LESSON
                                lessonElement.classList.add('locked-lesson');
                                const statusIcon = lessonElement.querySelector('.lesson-status i');
                                if (statusIcon) {
                                    statusIcon.className = 'bi bi-lock-fill text-white';
                                }

                                // Locked styles
                                lessonElement.style.opacity = '0.5';
                                lessonElement.style.textDecoration = 'none';
                                lessonElement.style.cursor = 'not-allowed';

                                // Add locked badge
                                const badge = document.createElement('span');
                                badge.className = 'lesson-badge badge bg-secondary ms-2';
                                badge.innerHTML = '<i class="bi bi-lock"></i> Locked';
                                lessonElement.appendChild(badge);

                                // Disable click for locked lessons
                                lessonElement.onclick = (e) => {
                                    e.preventDefault();
                                    showNotification('🔒 Complete the previous lesson to unlock this one', 'warning');
                                };
                            }
                        });

                    }

                    /**
                     * Renderizar contenido de lección
                     */
                    function renderLessonContent(lesson) {
                        const container = document.getElementById('lessonContentArea');

                        const isCompleted = asgState.courseProgress &&
                            asgState.courseProgress.completed_lessons_list &&
                            asgState.courseProgress.completed_lessons_list.includes(parseInt(lesson.id));

                        // Detectar tipo de lección con múltiples fallbacks
                        let lessonType = lesson.type || lesson.type_lesson || lesson.lesson_type || 'video';

                        // Debug logging
                        console.log('Renderizando lección:', {
                            id: lesson.id,
                            title: lesson.title,
                            type: lesson.type,
                            type_lesson: lesson.type_lesson,
                            lesson_type: lesson.lesson_type,
                            detected_type: lessonType,
                            has_video_url: !!lesson.video_url,
                            content_preview: lesson.content ? lesson.content.substring(0, 100) : 'No content'
                        });

                        // Si no hay video_url pero hay contenido, determinar si es quiz o text
                        if (lessonType === 'video' && !lesson.video_url && lesson.content) {
                            try {
                                const parsedContent = JSON.parse(lesson.content);
                                if (parsedContent.questions && Array.isArray(parsedContent.questions)) {
                                    lessonType = 'quiz';
                                } else {
                                    lessonType = 'text';
                                }
                            } catch (e) {
                                lessonType = 'text';
                            }
                        }

                        // Actualizar información de la lección en el header
                        updateLessonInfo(lesson);

                        let contentHtml = '';

                        // Lesson header
                        const typeConfig = {
                            video: {
                                icon: '',
                                label: ''
                            },
                            quiz: {
                                icon: '',
                                label: ''
                            },
                            text: {
                                icon: '',
                                label: ''
                            }
                        };

                        const currentType = typeConfig[lessonType] || typeConfig.text;

                        contentHtml += `
                    <div class="lesson-header">
                        <div class="lesson-type-badge lesson-type-${lessonType}">
                            <i class="bi bi-${currentType.icon} me-1"></i>
                            ${currentType.label}
                        </div>
                       
                     
                    </div>
                `;

                        // Contenido según tipo de lección detectado
                        if (lessonType === 'video') {
                            contentHtml += renderVideoContent(lesson);
                        } else if (lessonType === 'quiz') {
                            contentHtml += renderQuizContent(lesson);
                        } else if (lessonType === 'text') {
                            // Usar sistema de plantillas para lecciones de texto
                            contentHtml += renderTextContentWithTemplate(lesson);
                        } else {
                            // Fallback para tipos desconocidos
                            contentHtml += renderTextContentWithTemplate(lesson);
                        }



                        container.innerHTML = contentHtml;



                        // Actualizar navegación con los botones existentes del diseño
                        updateNavigationSection(lesson, isCompleted);
                        updateNavigationButtons();

                        // Initialize comments system for this lesson
                        //                 setTimeout(() => {
                        //                     initializeComments();
                        //                 }, 500); // Small delay to ensure content is fully loaded
                    }

                    /**
                     * Actualizar información de la lección en el header
                     */
                    function updateLessonInfo(lesson) {
                        const navInfo = getNavigationInfo();

                        // Actualizar número de lección
                        const lessonNumber = document.getElementById('lessonNumber');
                        if (lessonNumber) {
                            lessonNumber.textContent = `Lesson N.${navInfo.currentIndex + 1}`;
                        }

                        // Actualizar número de lección grande en navegación
                        const lessonNumberLarge = document.getElementById('lessonNumberLarge');
                        if (lessonNumberLarge) {
                            lessonNumberLarge.textContent = `Lesson: #${navInfo.currentIndex + 1}`;
                        }

                        // Actualizar título de lección
                        const lessonTitle = document.getElementById('lessonTitle');
                        if (lessonTitle) {
                            lessonTitle.textContent = lesson.title;
                        }

                        // Actualizar información del módulo
                        const moduleInfo = document.getElementById('moduleInfo');
                        if (moduleInfo) {
                            moduleInfo.textContent = lesson.module_title ? `Module ${lesson.module_title}` : 'Module 1.1';
                        }
                    }

                    /**
                     * Renderizar contenido de video para el nuevo diseño
                     */
                    function renderVideoContentNew(lesson) {
                        let contentHtml = `
                    <div class="lesson-header">
                        <h1 class="lesson-title">${lesson.title}</h1>
                        <p class="lesson-description">${lesson.description || 'Video lesson content'}</p>
                    </div>
                `;

                        if (lesson.video_url) {
                            contentHtml += `
                        <div class="video-container">
                            <iframe src="${lesson.video_url}"
                                    allowfullscreen>
                            </iframe>
                        </div>
                    `;
                        }

                        if (lesson.content) {
                            contentHtml += `
                        <div class="content-text">
                            ${lesson.content}
                        </div>
                    `;
                        }

                        return contentHtml;
                    }

                    /**
                     * Renderizar contenido de texto para el nuevo diseño
                     */
                    function renderTextContentNew(lesson) {
                        // Obtener imagen de la lección
                        let lessonImage = lesson.cover_img;
                        if (!lessonImage && lesson.module_cover_img) {
                            lessonImage = lesson.module_cover_img;
                        }
                        if (!lessonImage && asgState.currentCourse) {
                            lessonImage = asgState.currentCourse.cover_img;
                        }

                        let contentHtml = `
                    <div class="lesson-header">
                        <h1 class="lesson-title">Understanding The Basics of Marketing</h1>
                        <p class="lesson-description">Marketing is the discipline of identifying market needs and wants, then developing and offering a product that satisfies them, culminating in a successful transaction and, crucially, the loyalty of a satisfied customer.</p>
                    </div>
                `;

                        // Imagen de ejemplo (como en el diseño)
                        contentHtml += `
                    <div class="content-image">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDUwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjhGOUZBIiBzdHJva2U9IiNFOUVDRUYiLz4KPHN2ZyB4PSI1MCIgeT0iNTAiIHdpZHRoPSI0MDAiIGhlaWdodD0iMjAwIj4KICA8cGF0aCBkPSJNNTAgMjAwTDEwMCAxNTBMMTUwIDEwMEwyMDAgMTUwTDI1MCA1MEwzMDAgMTAwTDM1MCA3NUw0MDAgMTI1IiBzdHJva2U9IiMwQzFCNDEiIHN0cm9rZS13aWR0aD0iMyIgZmlsbD0ibm9uZSIvPgogIDx0ZXh0IHg9IjIwMCIgeT0iMTAwIiBmb250LWZhbWlseT0iT3V0Zml0IiBmb250LXNpemU9IjQ4IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iIzBDMUI0MSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RU1QPC90ZXh0Pgo8L3N2Zz4KPC9zdmc+" alt="Marketing Diagram" />
                    </div>
                `;

                        // Contenido de texto estructurado como en el diseño
                        contentHtml += `
                    <div class="content-section">
                        <div class="content-text">
                            <p>Personal Marketing applies this same fundamental principle to professional development. In this context, the professional is the "product," and the employer is the "customer." The process requires you to:</p>
                        </div>

                        <div class="highlight-box">
                            <h4>Identify the Employer's Need:</h4>
                            <p>Thoroughly understand the company's requirements, challenges, and objectives.</p>

                            <h4>Provide the Solution:</h4>
                            <p>Possess or develop the necessary skills to meet those needs.</p>

                            <h4>Communicate Your Value:</h4>
                            <p>Convince the employer that you are the right person to deliver that solution, securing not only the job but also your employer's full satisfaction upon seeing the results.</p>
                        </div>

                        <div class="content-text">
                            <p>To execute an effective personal marketing strategy, a clear understanding of your most important asset—yourself—is essential. Analyze your skills, define your exact value, and determine how you can directly contribute to the company's profitability and success.</p>
                        </div>
                    </div>
                `;

                        return contentHtml;
                    }

                    /**
                     * Renderizar contenido de quiz para el nuevo diseño
                     */
                    function renderQuizContentNew(lesson) {
                        let contentHtml = `
                    <div class="lesson-header">
                        <h1 class="lesson-title">${lesson.title}</h1>
                        <p class="lesson-description">Interactive Exercise to test your knowledge</p>
                    </div>
                `;

                        try {
                            const quizData = JSON.parse(lesson.content);
                            if (quizData.questions && Array.isArray(quizData.questions)) {
                                contentHtml += `
                            <div class="quiz-container">
                                <div class="quiz-question">
                                    <h3>${quizData.questions[0].question}</h3>
                                </div>
                                <div class="quiz-options">
                        `;

                                quizData.questions[0].options.forEach((option, index) => {
                                    contentHtml += `
                                <div class="quiz-option" onclick="selectQuizOption(${index}, false)">
                                    <input type="radio" name="question_0" value="${index}" style="pointer-events: none;">
                                    <span class="option-text">${option}</span>
                                </div>
                            `;
                                });

                                contentHtml += `
                                </div>
                            </div>
                        `;
                            }
                        } catch (e) {
                            contentHtml += `<div class="content-text"><p>Interactive Exercise could not be loaded.</p></div>`;
                        }

                        return contentHtml;
                    }

                    /**
                     * Actualizar sección de navegación
                     */
                    function updateNavigationSection(lesson, isCompleted) {
                        const navInfo = getNavigationInfo();

                        // Actualizar contador de lecciones
                        const lessonCounter = document.getElementById('lessonCounter');
                        if (lessonCounter) {
                            lessonCounter.textContent = `${navInfo.currentIndex + 1} of ${navInfo.totalLessons} Lessons`;
                        }

                        // Actualizar botón anterior
                        const btnPrevLesson = document.getElementById('btnPrevLesson');
                        if (btnPrevLesson) {
                            btnPrevLesson.disabled = !navInfo.hasPrevious;
                            btnPrevLesson.onclick = () => navigateToPrevious();

                            // Actualizar texto del botón
                            if (navInfo.hasPrevious) {
                                btnPrevLesson.innerHTML = `
                            <i class="bi bi-arrow-left"></i>
                            Previous Lesson
                        `;
                                btnPrevLesson.title = navInfo.previousLesson?.title || 'Previous Lesson';
                            } else {
                                btnPrevLesson.innerHTML = `
                            <i class="bi bi-arrow-left"></i>
                            Previous Lesson
                        `;
                                btnPrevLesson.title = 'This is the first lesson';
                            }
                        }

                        // Actualizar botón siguiente
                        const btnNextLesson = document.getElementById('btnNextLesson');
                        if (btnNextLesson) {
                            // For admins, don't check if lesson is locked
                            const isNextLocked = ASG_CONFIG.IS_ADMIN ? false : isLessonLocked(navInfo.nextLesson?.id);
                            const canNavigateNext = navInfo.hasNext && !isNextLocked;
                            btnNextLesson.disabled = !canNavigateNext;
                            btnNextLesson.onclick = () => navigateToNext();

                            // Actualizar texto del botón
                            if (navInfo.hasNext) {
                                // Check if next lesson is a quiz to change button text
                                const isNextLessonQuiz = navInfo.nextLesson?.type === 'quiz';
                                const buttonText = isNextLessonQuiz ? 'Continue to Exercise' : 'Next Lesson';
                                const buttonIcon = isNextLessonQuiz ? 'bi-play-circle' : 'bi-arrow-right';

                                // Debug logging
                                console.log('🔍 Next lesson button update:', {
                                    nextLessonTitle: navInfo.nextLesson?.title,
                                    nextLessonType: navInfo.nextLesson?.type,
                                    isNextLessonQuiz: isNextLessonQuiz,
                                    buttonText: buttonText,
                                    buttonIcon: buttonIcon
                                });

                                if (canNavigateNext) {
                                    btnNextLesson.innerHTML = `
                                ${buttonText}
                                <i class="bi ${buttonIcon}"></i>
                            `;
                                    btnNextLesson.title = navInfo.nextLesson?.title || buttonText;
                                } else {
                                    btnNextLesson.innerHTML = `
                                ${buttonText}
                                <i class="bi ${buttonIcon}"></i>
                            `;
                                    btnNextLesson.title = ASG_CONFIG.IS_ADMIN ? 'Navigate to next lesson' : 'Complete this lesson to continue';
                                }
                            } else {
                                btnNextLesson.innerHTML = `
                            Course Complete
                            <i class="bi bi-check-circle"></i>
                        `;
                                btnNextLesson.title = 'You have completed all lessons!';
                            }
                        }

                        // Actualizar botón de completar
                        const btnMarkComplete = document.getElementById('btnMarkComplete');
                        const btnViewCertificate = document.getElementById('btnViewCertificate');

                        // Check if this is the last lesson and course is 100% complete
                        const isLastLesson = !navInfo.hasNext;
                        const isCourseComplete = asgState.courseProgress && asgState.courseProgress.progress_percentage >= 100;
                        const shouldHideCompleteButton = isLastLesson && isCompleted && isCourseComplete;


                        if (btnMarkComplete) {
                            // Check if this is a quiz lesson - if so, hide the button completely
                            const lessonType = lesson.type || lesson.type_lesson || lesson.lesson_type || 'video';
                            const isQuizLesson = lessonType === 'quiz';

                            if (isQuizLesson) {
                                // Hide the complete button completely for quiz lessons
                                btnMarkComplete.style.display = 'none';
                                console.log('🚫 Complete button hidden for quiz lesson');
                            } else if (shouldHideCompleteButton) {
                                // Hide the complete button when course is finished
                                btnMarkComplete.style.display = 'none';
                                console.log('🎉 Complete button hidden - course finished!');
                            } else if (isCompleted) {
                                // Show completed state but keep button visible
                                btnMarkComplete.style.display = 'flex';
                                btnMarkComplete.innerHTML = '<i class="bi bi-check-circle-fill"></i> Completed';
                                btnMarkComplete.disabled = true;
                                btnMarkComplete.classList.add('completed');
                                btnMarkComplete.style.background = '#6c757d';
                                btnMarkComplete.style.color = 'white';
                                btnMarkComplete.onclick = null;
                                console.log('✅ Button set to completed state');
                            } else {
                                // Show active complete button
                                btnMarkComplete.style.display = 'flex';
                                btnMarkComplete.innerHTML = '<i class="bi bi-check-circle"></i> Mark as Complete';
                                btnMarkComplete.disabled = false;
                                btnMarkComplete.classList.remove('completed');
                                btnMarkComplete.style.background = '#28a745';
                                btnMarkComplete.style.color = 'white';
                                btnMarkComplete.onclick = () => {
                                    console.log('🎯 Complete button clicked for lesson:', lesson.id);
                                    markLessonComplete(lesson.id);
                                };
                                console.log('🔘 Button set to active state');
                            }
                        } else {
                            console.error('❌ btnMarkComplete element not found in DOM');
                        }

                        // Update certificate button visibility
                        if (btnViewCertificate) {
                            if (isCourseComplete) {
                                btnViewCertificate.classList.add('show');
                                console.log('🏆 Certificate button shown - course completed!');
                            } else {
                                btnViewCertificate.classList.remove('show');
                                console.log('📋 Certificate button hidden - course not completed');
                            }
                        } else {
                            console.warn('⚠️ btnViewCertificate element not found in DOM');
                        }

                        // Update lesson-info-section navigation buttons
                        const btnPrevLessonInfo = document.getElementById('btnPrevLessonInfo');
                        const btnNextLessonInfo = document.getElementById('btnNextLessonInfo');

                        if (btnPrevLessonInfo) {
                            btnPrevLessonInfo.disabled = !navInfo.hasPrevious;
                            btnPrevLessonInfo.title = navInfo.hasPrevious ? navInfo.previousLesson?.title || 'Previous Lesson' : 'This is the first lesson';
                            btnPrevLessonInfo.onclick = navInfo.hasPrevious ? () => navigateToPrevious() : null;
                        }

                        if (btnNextLessonInfo) {
                            // For admins, don't check if lesson is locked
                            const isNextLocked = ASG_CONFIG.IS_ADMIN ? false : isLessonLocked(navInfo.nextLesson?.id);
                            const canNavigateNext = navInfo.hasNext && !isNextLocked;
                            btnNextLessonInfo.disabled = !canNavigateNext;

                            if (navInfo.hasNext) {
                                const isNextLessonQuiz = navInfo.nextLesson?.type === 'quiz';
                                const buttonText = isNextLessonQuiz ? 'Exercise' : 'Next';

                                btnNextLessonInfo.innerHTML = `
                            ${buttonText}
                            <i class="bi bi-arrow-right"></i>
                        `;
                                btnNextLessonInfo.title = canNavigateNext ? navInfo.nextLesson?.title || buttonText : (ASG_CONFIG.IS_ADMIN ? 'Navigate to next lesson' : 'Complete this lesson to continue');
                                btnNextLessonInfo.onclick = canNavigateNext ? () => navigateToNext() : null;
                            } else {
                                btnNextLessonInfo.innerHTML = `
                            Complete
                            <i class="bi bi-check-circle"></i>
                        `;
                                btnNextLessonInfo.title = 'You have completed all lessons!';
                                btnNextLessonInfo.onclick = null;
                            }
                        }
                    }





                    /**
                     * Inicializar eventos del nuevo diseño
                     */
                    function initializeNewDesignEvents() {
                        // Botón de contenido del curso
                        const btnCourseContent = document.getElementById('btnCourseContent');
                        if (btnCourseContent) {
                            btnCourseContent.onclick = () => {
                                showCourseContentModal();
                            };
                        }



                    }

                    /**
                     * Renderizar contenido de texto con imagen (función original mantenida para compatibilidad)
                     */
                    function renderTextContent(lesson) {
                        let contentHtml = '';

                        // Verificar si hay imagen de lección específica
                        let lessonImage = lesson.cover_img;

                        // Si no hay imagen de lección, usar imagen del módulo o curso
                        if (!lessonImage) {
                            // Primero intentar usar la imagen del módulo desde los datos de la lección
                            if (lesson.module_cover_img) {
                                lessonImage = lesson.module_cover_img;
                            }
                            // Si no, buscar en la estructura del curso
                            else if (asgState.currentCourse && asgState.currentCourse.modules) {
                                const lessonModule = asgState.currentCourse.modules.find(module =>
                                    module.lessons && module.lessons.some(l => l.id_lesson == lesson.id)
                                );

                                if (lessonModule && lessonModule.cover_img) {
                                    lessonImage = lessonModule.cover_img;
                                }
                            }

                            // Si no hay imagen del módulo, usar imagen del curso
                            if (!lessonImage && asgState.currentCourse) {
                                lessonImage = asgState.currentCourse.cover_img;
                            }
                        }

                        // Fallback a imagen placeholder si no hay ninguna imagen
                        if (!lessonImage) {
                            lessonImage = 'https://via.placeholder.com/800x400/1e88e5/ffffff?text=Lección+de+Texto';
                        }

                        console.log('Renderizando contenido de texto:', {
                            lessonId: lesson.id,
                            lessonTitle: lesson.title,
                            hasContent: !!lesson.content,
                            images: {
                                lesson_cover: lesson.cover_img,
                                module_cover: lesson.module_cover_img,
                                course_cover: asgState.currentCourse?.cover_img,
                                final_image: lessonImage
                            },
                            imageSource: lessonImage?.includes('placeholder') ? 'placeholder' : lessonImage === lesson.cover_img ? 'lesson' : lessonImage === lesson.module_cover_img ? 'module' : lessonImage === asgState.currentCourse?.cover_img ? 'course' : 'unknown'
                        });

                        contentHtml += '<div class="text-lesson-container">';

                        // Procesar contenido de texto para separar primer párrafo
                        let firstParagraph = '';
                        let remainingContent = '';

                        if (lesson.content) {
                            // Obtener solo el texto sin HTML para buscar el primer punto
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = lesson.content;
                            const textContent = tempDiv.textContent || tempDiv.innerText || '';

                            // Buscar el primer punto en el texto
                            const firstDotIndex = textContent.indexOf('.');

                            if (firstDotIndex !== -1) {
                                // Encontró un punto, obtener el texto hasta el primer punto
                                const firstSentenceText = textContent.substring(0, firstDotIndex + 1).trim();
                                const remainingText = textContent.substring(firstDotIndex + 1).trim();

                                // Crear el primer párrafo con el texto hasta el primer punto
                                firstParagraph = `<p>${firstSentenceText}</p>`;

                                // Si hay texto restante, crear el contenido restante
                                if (remainingText) {
                                    // Buscar en el HTML original donde termina la primera oración
                                    const originalHtml = lesson.content;
                                    const firstDotInHtml = originalHtml.indexOf('.');

                                    if (firstDotInHtml !== -1) {
                                        // Obtener el HTML después del primer punto
                                        let afterDotHtml = originalHtml.substring(firstDotInHtml + 1).trim();

                                        // Si el contenido restante no está envuelto en párrafos, envolverlo
                                        if (afterDotHtml && !afterDotHtml.startsWith('<')) {
                                            afterDotHtml = `<p>${afterDotHtml}</p>`;
                                        }

                                        remainingContent = afterDotHtml;
                                    } else {
                                        // Fallback: usar el texto restante como párrafo
                                        remainingContent = `<p>${remainingText}</p>`;
                                    }
                                }
                            } else {
                                // No encontró punto, usar todo el contenido como primer párrafo
                                firstParagraph = `<p>${textContent}</p>`;
                                remainingContent = '';
                            }
                        }

                        // Layout con imagen y primer párrafo lado a lado
                        if (lessonImage && firstParagraph) {
                            const isLessonImage = lessonImage === lesson.cover_img;
                            const isModuleImage = lessonImage === lesson.module_cover_img;
                            const isCourseImage = lessonImage === asgState.currentCourse?.cover_img;
                            const isPlaceholder = lessonImage.includes('placeholder');

                            let captionText = 'Module Image';
                            if (isLessonImage) {
                                captionText = 'Lesson Image';
                            } else if (isModuleImage) {
                                captionText = 'Module Image';
                            } else if (isCourseImage) {
                                captionText = 'Course Image';
                            } else if (isPlaceholder) {
                                captionText = 'Example Image';
                            }

                            contentHtml += `
                        <div class="image-text-layout">
                            <div class="lesson-image-container">
                                <img src="${lessonImage}"
                                     alt="${lesson.title}"
                                     class="lesson-image"
                                     onerror="this.style.display='none'">
                                <div class="image-caption">
                                    <small class="text-white">
                                        ${captionText}
                                    </small>
                                </div>
                            </div>
                            <div class="first-paragraph">
                                ${firstParagraph}
                            </div>
                        </div>
                    `;

                            // Mostrar el resto del contenido debajo
                            if (remainingContent.trim()) {
                                contentHtml += `
                            <div class="remaining-content">
                                ${remainingContent}
                            </div>
                        `;
                            }
                        } else if (lessonImage) {
                            // Solo imagen, sin contenido
                            const isLessonImage = lessonImage === lesson.cover_img;
                            const isModuleImage = lessonImage === lesson.module_cover_img;
                            const isCourseImage = lessonImage === asgState.currentCourse?.cover_img;
                            const isPlaceholder = lessonImage.includes('placeholder');

                            let captionText = 'Imagen del módulo';
                            if (isLessonImage) {
                                captionText = 'Imagen de la lección';
                            } else if (isModuleImage) {
                                captionText = 'Imagen del módulo';
                            } else if (isCourseImage) {
                                captionText = 'Imagen del curso';
                            } else if (isPlaceholder) {
                                captionText = 'Imagen de ejemplo';
                            }

                            contentHtml += `
                        <div class="lesson-image-container">
                            <img src="${lessonImage}"
                                 alt="${lesson.title}"
                                 class="lesson-image"
                                 onerror="this.style.display='none'">
                            <div class="image-caption">
                                <small class="text-white">
                                    ${captionText}
                                </small>
                            </div>
                        </div>
                    `;
                        } else if (lesson.content) {
                            // Solo contenido, sin imagen
                            contentHtml += `
                        <div class="lesson-text-content">
                            ${lesson.content}
                        </div>
                    `;
                        } else {
                            // Ni imagen ni contenido
                            contentHtml += `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                           Content not available.
                        </div>
                    `;
                        }

                        contentHtml += '</div>';





                        return contentHtml;
                    }

                    /**
                     * Renderizar contenido de texto con sistema de plantillas
                     */
                    function renderTextContentWithTemplate(lesson) {
                        // Obtener la plantilla de la lección (por defecto 'basic')
                        const template = lesson.lesson_template || 'basic';

                        console.log('🎨 Renderizando lección con plantilla:', template);
                        console.log('📄 Datos de lección completos:', lesson);
                        console.log('🖼️ lesson_images:', lesson.lesson_images);
                        console.log('📝 lesson_sections:', lesson.lesson_sections);

                        // Llamar a la función específica según la plantilla
                        switch (template) {
                            case 'basic':
                                return renderBasicTemplate(lesson);
                            case 'featured_image':
                                return renderFeaturedImageTemplate(lesson);
                            case 'two_columns':
                                return renderTwoColumnsTemplate(lesson);
                            case 'hero_image':
                                return renderHeroImageTemplate(lesson);
                            case 'medium_right':
                                return renderMediumRightTemplate(lesson);
                            case 'medium_left':
                                return renderMediumLeftTemplate(lesson);
                            case 'triple_layout':
                                return renderTripleLayoutTemplate(lesson);
                            default:
                                console.warn('⚠️ Plantilla desconocida:', template, '- usando básica');
                                return renderBasicTemplate(lesson);
                        }
                    }

                    /**
                     * Process line breaks in content
                     */
                    function processLineBreaks(content) {
                        if (!content) return '';

                        // Convert escaped newlines to actual newlines, then to HTML breaks
                        return content
                            .replace(/\\n/g, '\n')  // Convert \\n to \n
                            .replace(/\n\n+/g, '</p><p>')  // Multiple newlines = new paragraphs
                            .replace(/\n/g, '<br>')  // Single newlines = line breaks
                            .replace(/^(.+)$/, '<p>$1</p>')  // Wrap in paragraph if not already
                            .replace(/<p><\/p>/g, '');  // Remove empty paragraphs
                    }

                    /**
                     * Plantilla Básica - Texto simple con título
                     */
                    function renderBasicTemplate(lesson) {
                        return `
                    <div class="lesson-template basic-template">
                        <div class="lesson-content-basic">
                            <h1 class="lesson-title-main">${lesson.title}</h1>
                            <div class="basic-text-content" data-edit-type="content">
                                ${lesson.content || '<p>No content available for this lesson.</p>'}
                            </div>
                        </div>
                    </div>
                `;
                    }

                    /**
                     * Plantilla Con Imagen Destacada - Imagen grande + contenido
                     */
                    function renderFeaturedImageTemplate(lesson) {
                        const imageData = getLessonImageData(lesson, 'main');

                        return `
                    <div class="lesson-template featured-image-template">
                        <div class="lesson-content-featured">
                            <h1 class="lesson-title-main">${lesson.title}</h1>
                            <div class="featured-image-container">
                                <img src="${imageData.url}" alt="${imageData.alt}" class="featured-image" data-image-type="main" />
                            </div>
                            <div class="featured-text-content" data-edit-type="content">
                                ${lesson.content || '<p>No content available for this lesson.</p>'}
                            </div>
                        </div>
                    </div>
                `;
                    }

                    /**
                     * Plantilla Dos Columnas - Imagen izquierda, texto derecha
                     */
                    function renderTwoColumnsTemplate(lesson) {
                        const imageData = getLessonImageData(lesson, 'left');

                        return `
                    <div class="lesson-template two-columns-template">
                        <div class="lesson-content-two-columns">
                            <h1 class="lesson-title-main">${lesson.title}</h1>
                            <div class="two-columns-container">
                                <div class="column-image">
                                    <img src="${imageData.url}" alt="${imageData.alt}" class="column-image-img" data-image-type="left" />
                                </div>
                                <div class="column-content" data-edit-type="content">
                                    ${lesson.content || '<p>No content available for this lesson.</p>'}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                    }

                    /**
                     * Plantilla Imagen Grande + Texto Abajo - Imagen hero + contenido debajo
                     */
                    function renderHeroImageTemplate(lesson) {
                        const imageData = getLessonImageData(lesson, 'hero');

                        return `
                    <div class="lesson-template hero-image-template">
                        <div class="lesson-content-hero">
                            <h1 class="lesson-title-main">${lesson.title}</h1>
                            <div class="hero-image-container">
                                <img src="${imageData.url}" alt="${imageData.alt}" class="hero-image" data-image-type="hero" />
                            </div>
                            <div class="hero-text-content" data-edit-type="content">
                                ${lesson.content || '<p>No content available for this lesson.</p>'}
                            </div>
                        </div>
                    </div>
                `;
                    }

                    /**
                     * Plantilla Imagen Mediana + Texto Derecha - Imagen mediana a la izquierda, texto a la derecha
                     */
                    function renderMediumRightTemplate(lesson) {
                        const imageData = getLessonImageData(lesson, 'mediumLeft');

                        return `
                    <div class="lesson-template medium-right-template">
                        <div class="lesson-content-medium">
                            <h1 class="lesson-title-main">${lesson.title}</h1>
                            <div class="medium-container">
                                <div class="medium-image-left">
                                    <img src="${imageData.url}" alt="${imageData.alt}" class="medium-image" data-image-type="mediumLeft" />
                                </div>
                                <div class="medium-content-right" data-edit-type="content">
                                    ${lesson.content || '<p>No content available for this lesson.</p>'}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                    }

                    /**
                     * Plantilla Imagen Mediana + Texto Izquierda - Imagen mediana a la derecha, texto a la izquierda
                     */
                    function renderMediumLeftTemplate(lesson) {
                        const imageData = getLessonImageData(lesson, 'mediumRight');

                        return `
                    <div class="lesson-template medium-left-template">
                        <div class="lesson-content-medium">
                            <h1 class="lesson-title-main">${lesson.title}</h1>
                            <div class="medium-container">
                                <div class="medium-content-left" data-edit-type="content">
                                    ${lesson.content || '<p>No content available for this lesson.</p>'}
                                </div>
                                <div class="medium-image-right">
                                    <img src="${imageData.url}" alt="${imageData.alt}" class="medium-image" data-image-type="mediumRight" />
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                    }

                    /**
                     * Función auxiliar para obtener la imagen de la lección
                     */
                    function getLessonImage(lesson, imageType = 'main') {
                        // Primero intentar obtener de lesson_images (nuevo sistema)
                        if (lesson.lesson_images) {
                            try {
                                const lessonImages = typeof lesson.lesson_images === 'string' ?
                                    JSON.parse(lesson.lesson_images) :
                                    lesson.lesson_images;

                                // Mapear tipos de imagen según la plantilla
                                const imageMapping = {
                                    'main': ['main', 'hero', 'left', 'mediumLeft', 'mediumRight'],
                                    'secondary': ['secondary'],
                                    'sidebar': ['sidebar']
                                };

                                // Buscar imagen del tipo solicitado
                                const possibleTypes = imageMapping[imageType] || [imageType];
                                for (const type of possibleTypes) {
                                    if (lessonImages[type] && lessonImages[type].url) {
                                        return lessonImages[type].url;
                                    }
                                }
                            } catch (e) {
                                console.warn('⚠️ Could not parse lesson_images:', e);
                            }
                        }

                        // Fallback al sistema anterior
                        let lessonImage = lesson.cover_img;

                        // Si no hay imagen de lección, usar imagen del módulo
                        if (!lessonImage && lesson.module_cover_img) {
                            lessonImage = lesson.module_cover_img;
                        }

                        // Si no hay imagen del módulo, usar imagen del curso
                        if (!lessonImage && asgState.currentCourse) {
                            lessonImage = asgState.currentCourse.cover_img;
                        }

                        // Fallback a imagen placeholder si no hay ninguna imagen
                        if (!lessonImage) {
                            lessonImage = 'https://via.placeholder.com/800x400/1e88e5/ffffff?text=Lesson+Image';
                        }

                        return lessonImage;
                    }

                    /**
                     * Función auxiliar para obtener datos específicos de imagen
                     */
                    function getLessonImageData(lesson, imageType) {
                        if (lesson.lesson_images) {
                            try {
                                const lessonImages = typeof lesson.lesson_images === 'string' ?
                                    JSON.parse(lesson.lesson_images) :
                                    lesson.lesson_images;

                                if (lessonImages[imageType]) {
                                    return {
                                        url: lessonImages[imageType].url || getLessonImage(lesson, 'main'),
                                        alt: lessonImages[imageType].alt || lesson.title,
                                        description: lessonImages[imageType].description || ''
                                    };
                                }
                            } catch (e) {
                                console.warn('⚠️ Could not parse lesson_images:', e);
                            }
                        }

                        // Fallback
                        return {
                            url: getLessonImage(lesson, 'main'),
                            alt: lesson.title,
                            description: ''
                        };
                    }

                    /**
                     * Plantilla Triple Layout - Hero + Izquierda + Derecha
                     */
                    function renderTripleLayoutTemplate(lesson) {
                        // Parse sections data
                        let sectionsData = {};
                        if (lesson.lesson_sections) {
                            try {
                                sectionsData = typeof lesson.lesson_sections === 'string' ?
                                    JSON.parse(lesson.lesson_sections) :
                                    lesson.lesson_sections;
                            } catch (e) {
                                console.warn('⚠️ Could not parse lesson_sections for triple layout:', e);
                            }
                        }

                        // Get images for each section
                        const heroImageData = getLessonImageData(lesson, 'tripleHero');
                        const leftImageData = getLessonImageData(lesson, 'tripleLeft');
                        const rightImageData = getLessonImageData(lesson, 'tripleRight');

                        // Get content for each section and process line breaks
                        const heroContent = processLineBreaks(sectionsData.hero_content || lesson.content || '');
                        const leftContent = processLineBreaks(sectionsData.left_content || '');
                        const rightContent = processLineBreaks(sectionsData.right_content || '');

                        return `
                    <div class="lesson-template triple-layout-template">
                        <!-- Hero Section -->
                        <div class="triple-hero-section">
                            <h1 class="lesson-title-main">${lesson.title}</h1>
                            <div class="hero-image-container">
                                <img src="${heroImageData.url}" alt="${heroImageData.alt}" class="hero-image" data-image-type="tripleHero" />
                            </div>
                            ${heroContent ? `<div class="hero-content" data-edit-type="hero_content">${heroContent}</div>` : ''}
                        </div>

                        <!-- Left Section (Image Left + Content Right) -->
                        ${leftContent || leftImageData.url !== heroImageData.url ? `
                        <div class="triple-left-section">
                            <div class="section-container">
                                <div class="section-image-left">
                                    <img src="${leftImageData.url}" alt="${leftImageData.alt}" class="section-image" data-image-type="tripleLeft" />
                                </div>
                                <div class="section-content-right" data-edit-type="left_content">
                                    ${leftContent || '<p>Left section content</p>'}
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        <!-- Right Section (Content Left + Image Right) -->
                        ${rightContent || rightImageData.url !== heroImageData.url ? `
                        <div class="triple-right-section">
                            <div class="section-container">
                                <div class="section-content-left" data-edit-type="right_content">
                                    ${rightContent || '<p>Right section content</p>'}
                                </div>
                                <div class="section-image-right">
                                    <img src="${rightImageData.url}" alt="${rightImageData.alt}" class="section-image" data-image-type="tripleRight" />
                                </div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                `;
                    }

                    /**
                     * Renderizar contenido de video
                     */
                    function renderVideoContent(lesson) {
                        if (!lesson.video_url) {
                            // Si no hay video pero hay contenido, mostrar el contenido
                            if (lesson.content) {
                                return `
                            <div class="lesson-content-text">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                   This lessons has content of text.
                                </div>
                                <div class="content-body">
                                    ${lesson.content}
                                </div>
                            </div>
                        `;
                            }
                            return `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Video not found it.
                            <br><small class="text-white">Contact us.</small>
                        </div>
                    `;
                        }

                        // Convertir URL de YouTube a formato embed si es necesario
                        let embedUrl = lesson.video_url;
                        if (lesson.video_url.includes('youtube.com/watch')) {
                            const videoId = lesson.video_url.split('v=')[1]?.split('&')[0];
                            if (videoId) {
                                embedUrl = `https://www.youtube.com/embed/${videoId}`;
                            }
                        } else if (lesson.video_url.includes('youtu.be/')) {
                            const videoId = lesson.video_url.split('youtu.be/')[1]?.split('?')[0];
                            if (videoId) {
                                embedUrl = `https://www.youtube.com/embed/${videoId}`;
                            }
                        }

                        return `
                    <div class="video-container">
                        <iframe src="${embedUrl}"
                                allowfullscreen
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                        </iframe>
                    </div>
                    ${lesson.content ? `<div class="lesson-description">${lesson.content}</div>` : ''}
                `;
                    }

                    /**
                     * Crear quiz de ejemplo si no hay datos
                     */
                    function createSampleQuiz(lesson) {
                        return {
                            title: lesson.title + ' - Quiz',
                            description: 'Test your knowledge with this interactive quiz.',
                            passing_score: 70,
                            questions: [{
                                    id: 1,
                                    type: 'multiple_choice',
                                    question: 'What did you learn in this lesson?',
                                    options: [
                                        'Important concepts and key points',
                                        'Nothing significant',
                                        'Basic information only',
                                        'Advanced techniques'
                                    ],
                                    correct_answer: 0,
                                    allow_multiple: false
                                },
                                {
                                    id: 2,
                                    type: 'true_false',
                                    question: 'Did you understand the main concepts of this lesson?',
                                    correct_answer: true
                                }
                            ]
                        };
                    }

                    /**
                     * Renderizar contenido de quiz
                     */
                    function renderQuizContent(lesson) {
                        // Parse quiz data from content_lesson
                        let quizData;
                        try {
                            quizData = lesson.content ? JSON.parse(lesson.content) : null;
                        } catch (e) {
                            quizData = null;
                        }

                        // If no quiz data, create a sample quiz
                        if (!quizData || !quizData.questions) {
                            quizData = createSampleQuiz(lesson);
                        }

                        // Store quiz data globally
                        asgState.currentQuiz = quizData;
                        asgState.quizAnswers = [];
                        asgState.currentQuestionIndex = 0;
                        asgState.quizScore = 0;

                        // Hide Mark as Complete button for quiz lessons
                        hideCompleteButtonForQuiz();

                        return `
                    <div class="quiz-lesson-container">
                        <div class="quiz-intro" id="quizIntro">
                            <div class="quiz-intro-content">
                                <h3><i class="bi bi-trophy me-2"></i>${quizData.title || lesson.title}</h3>
                                <p class="quiz-description">${quizData.description || 'Test your knowledge with this interactive quiz.'}</p>

                                <div class="quiz-stats">
                                    <div class="stat-item">
                                        <i class="bi bi-question-circle me-2"></i>
                                        <span>${quizData.questions.length} Questions</span>
                                    </div>
                          

                                </div>

                                <button class="btn btn-lg start-quiz-btn" style="background: #0C1B40; color: white;" onclick="startQuiz()">
                                    <i class="bi bi-play-circle me-2"></i>Start Exercise
                                </button>
                            </div>
                        </div>

                        <div class="quiz-content" id="quizContent" style="display: none;">
                            <!-- Quiz questions will be rendered here -->
                        </div>

                        <div class="quiz-results" id="quizResults" style="display: none;">
                            <!-- Quiz results will be shown here -->
                        </div>
                    </div>
                `;
                    }

                    /**
                     * Crear quiz de ejemplo si no hay datos
                     */

                    /**
                     * Actualizar botón de completar lección
                     */
                    function updateLessonCompleteButton(lessonId, isCompleted) {
                        // Use the same logic as renderTextContentWithTemplate
                        const btnMarkComplete = document.getElementById('btnMarkComplete');
                        const btnViewCertificate = document.getElementById('btnViewCertificate');

                        if (!btnMarkComplete) {
                            console.warn('⚠️ btnMarkComplete not found in updateLessonCompleteButton');
                            return;
                        }

                        // Get current lesson to check if it's the last one
                        const currentLesson = asgState.lessonsList.find(l => l.id == lessonId);
                        if (!currentLesson) {
                            console.warn('⚠️ Current lesson not found for ID:', lessonId);
                            return;
                        }

                        // Find next lesson
                        const currentIndex = asgState.lessonsList.findIndex(l => l.id == lessonId);
                        const nextLesson = currentIndex < asgState.lessonsList.length - 1 ?
                            asgState.lessonsList[currentIndex + 1] : null;

                        // Check if this is the last lesson and course is 100% complete
                        const isLastLesson = !nextLesson;
                        const isCourseComplete = asgState.courseProgress && asgState.courseProgress.progress_percentage >= 100;
                        const shouldHideCompleteButton = isLastLesson && isCompleted && isCourseComplete;

                        console.log('🔄 Updating complete button after completion:', {
                            lessonId: lessonId,
                            isCompleted: isCompleted,
                            isLastLesson: isLastLesson,
                            isCourseComplete: isCourseComplete,
                            shouldHideCompleteButton: shouldHideCompleteButton
                        });

                        if (shouldHideCompleteButton) {
                            // Hide the complete button when course is finished
                            btnMarkComplete.style.display = 'none';
                            console.log('🎉 Complete button hidden after completion - course finished!');
                        } else if (isCompleted) {
                            // Show completed state but keep button visible
                            btnMarkComplete.style.display = 'flex';
                            btnMarkComplete.innerHTML = '<i class="bi bi-check-circle-fill"></i> Completed';
                            btnMarkComplete.disabled = true;
                            btnMarkComplete.classList.add('completed');
                            btnMarkComplete.style.background = '#6c757d';
                            btnMarkComplete.style.color = 'white';
                            btnMarkComplete.onclick = null;
                        } else {
                            // Show active complete button
                            btnMarkComplete.style.display = 'flex';
                            btnMarkComplete.innerHTML = '<i class="bi bi-check-circle"></i> Mark as Complete';
                            btnMarkComplete.disabled = false;
                            btnMarkComplete.classList.remove('completed');
                            btnMarkComplete.style.background = '#28a745';
                            btnMarkComplete.style.color = 'white';
                            btnMarkComplete.onclick = () => {
                                markLessonComplete(lessonId);
                            };
                        }

                        // Also update certificate button visibility
                        if (btnViewCertificate) {
                            if (isCourseComplete) {
                                btnViewCertificate.classList.add('show');
                                console.log('🏆 Certificate button shown after completion!');
                            } else {
                                btnViewCertificate.classList.remove('show');
                                console.log('📋 Certificate button hidden - course not completed');
                            }
                        } else {
                            console.warn('⚠️ btnViewCertificate not found in updateLessonCompleteButton');
                        }
                    }

                    /**
                     * Toggle de módulos (expandir/colapsar)
                     */
                    function toggleModule(moduleId) {
                        const module = document.getElementById(moduleId);
                        const chevron = document.getElementById(`chevron-${moduleId}`);

                        if (!module || !chevron) return;

                        if (module.classList.contains('show')) {
                            // Colapsar módulo
                            module.classList.remove('show');
                            chevron.classList.remove('rotated');
                        } else {
                            // Expandir módulo (opcional: colapsar otros)
                            // Colapsar otros módulos
                            document.querySelectorAll('.module-lessons.show').forEach(openModule => {
                                if (openModule.id !== moduleId) {
                                    openModule.classList.remove('show');
                                    const otherChevron = document.getElementById(`chevron-${openModule.id}`);
                                    if (otherChevron) {
                                        otherChevron.classList.remove('rotated');
                                    }
                                }
                            });

                            // Expandir módulo actual
                            module.classList.add('show');
                            chevron.classList.add('rotated');
                        }
                    }

                    /**
                     * Configurar navegación móvil
                     */
                    function setupMobileNavigation() {
                        const sidebarToggle = document.getElementById('sidebarToggle');
                        const sidebar = document.getElementById('lessonSidebar');
                        const layout = document.querySelector('.lesson-layout');

                        if (sidebarToggle && sidebar && layout) {
                            sidebarToggle.addEventListener('click', function(e) {
                                e.stopPropagation();
                                sidebar.classList.toggle('show');
                                layout.classList.toggle('sidebar-open');
                            });

                            // Cerrar sidebar al hacer clic fuera en móvil
                            document.addEventListener('click', function(e) {
                                if (window.innerWidth <= 992) {
                                    if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                                        sidebar.classList.remove('show');
                                        layout.classList.remove('sidebar-open');
                                    }
                                }
                            });

                            // Cerrar sidebar al navegar a una lección en móvil
                            sidebar.addEventListener('click', function(e) {
                                if (e.target.closest('.lesson-item') && window.innerWidth <= 992) {
                                    setTimeout(() => {
                                        sidebar.classList.remove('show');
                                        layout.classList.remove('sidebar-open');
                                    }, 300);
                                }
                            });
                        }
                    }

                    // ===== NAVEGACIÓN =====

                    /**
                     * Obtener lista ordenada de todas las lecciones
                     */
                    function getAllLessonsOrdered() {
                        const allLessons = [];

                        if (asgState.currentCourse && asgState.currentCourse.modules) {
                            asgState.currentCourse.modules.forEach(module => {
                                if (module.lessons) {
                                    module.lessons.forEach(lesson => {
                                        allLessons.push({
                                            id: parseInt(lesson.id_lesson),
                                            title: lesson.title_lesson,
                                            type: lesson.lesson_type || lesson.type || 'video', // Include lesson type
                                            module: module.title_module
                                        });
                                    });
                                }
                            });
                        }

                        const sortedLessons = allLessons.sort((a, b) => a.id - b.id);

                        // Debug logging for lesson types
                        console.log('📚 All lessons ordered with types:', sortedLessons.map(l => ({
                            id: l.id,
                            title: l.title.substring(0, 30) + '...',
                            type: l.type
                        })));

                        return sortedLessons;
                    }

                    /**
                     * Encontrar índice de lección actual
                     */
                    function getCurrentLessonIndex() {
                        const allLessons = getAllLessonsOrdered();
                        const currentLessonId = parseInt(ASG_CONFIG.LESSON_ID);

                        return allLessons.findIndex(lesson => lesson.id === currentLessonId);
                    }

                    /**
                     * Navigate to previous lesson
                     */
                    function navigateToPrevious() {
                        const allLessons = getAllLessonsOrdered();
                        const currentIndex = getCurrentLessonIndex();

                        if (currentIndex <= 0) {
                            showNotification('This is the first lesson', 'info');
                            return;
                        }

                        const previousLesson = allLessons[currentIndex - 1];

                        // Previous lessons are always accessible
                        console.log('📖 Navigating to previous lesson:', previousLesson.title);
                        navigateToLesson(previousLesson.id);
                    }



                    /**
                     * Navigate to next lesson (Sequential Learning)
                     */
                    async function navigateToNext() {
                        const allLessons = getAllLessonsOrdered();
                        const currentIndex = getCurrentLessonIndex();

                        if (currentIndex >= allLessons.length - 1) {
                            showNotification('🎉 This is the last lesson! Congratulations!', 'success');
                            return;
                        }

                        const nextLesson = allLessons[currentIndex + 1];
                        const currentLesson = allLessons[currentIndex];

                        // Show current progress state BEFORE refresh
                        console.log('📋 BEFORE REFRESH - Current Progress State:', {
                            courseProgress: asgState.courseProgress,
                            completedLessons: asgState.courseProgress?.completed_lessons_list || [],
                            currentLessonId: currentLesson.id,
                            nextLessonId: nextLesson.id,
                            isNextLocked: isLessonLocked(nextLesson.id)
                        });



                        // For admins, skip progress refresh and lock checks
                        if (ASG_CONFIG.IS_ADMIN) {
                            console.log('🔧 Admin user - skipping progress checks and lock validation');
                        } else {
                            // Force refresh progress before checking lock status
                            console.log('🔄 Refreshing progress before navigation...');
                            await loadUserProgress();

                            // Check if next lesson is unlocked after refresh
                            const isNextLockedAfterRefresh = isLessonLocked(nextLesson.id);
                            console.log('🔍 Next lesson lock status after refresh:', {
                                nextLessonId: nextLesson.id,
                                isLocked: isNextLockedAfterRefresh,
                                completedLessons: asgState.courseProgress?.completed_lessons_list || []
                            });

                            if (isNextLockedAfterRefresh) {
                            // Check if current lesson is completed
                            const completedLessons = asgState.courseProgress?.completed_lessons_list || [];
                            const isCurrentCompleted = completedLessons.includes(parseInt(currentLesson.id));

                            if (!isCurrentCompleted) {
                                showNotification('⚠️ Complete this lesson first to unlock the next one', 'warning');
                                // Highlight the complete button
                                const completeBtn = document.querySelector('.btn-complete, #btnMarkComplete');
                                if (completeBtn && !completeBtn.disabled) {
                                    completeBtn.style.animation = 'pulse 1s infinite';
                                    completeBtn.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'center'
                                    });
                                    setTimeout(() => {
                                        completeBtn.style.animation = '';
                                    }, 3000);
                                }
                            } else {
                                showNotification('🔒 Next lesson is still locked. There might be a synchronization issue.', 'warning');
                                console.warn('⚠️ Current lesson is completed but next is still locked:', {
                                    currentLessonId: currentLesson.id,
                                    nextLessonId: nextLesson.id,
                                    completedLessons: completedLessons
                                });
                            }
                            return;
                        }
                        } // End of non-admin check

                        console.log('📖 Navigating to next lesson:', nextLesson.title);

                        // Ensure we have a clean lesson ID (remove any extra characters)
                        let cleanLessonId = nextLesson.id;
                        if (typeof cleanLessonId === 'string' && cleanLessonId.includes(':')) {
                            cleanLessonId = cleanLessonId.split(':')[0];
                        }
                        cleanLessonId = parseInt(cleanLessonId);

                        console.log('🔧 Clean lesson ID:', cleanLessonId);
                        navigateToLesson(cleanLessonId);
                    }

                    /**
                     * Obtener información de navegación
                     */
                    function getNavigationInfo() {
                        const allLessons = getAllLessonsOrdered();
                        const currentIndex = getCurrentLessonIndex();

                        return {
                            currentIndex: currentIndex,
                            totalLessons: allLessons.length,
                            hasPrevious: currentIndex > 0,
                            hasNext: currentIndex < allLessons.length - 1,
                            previousLesson: currentIndex > 0 ? allLessons[currentIndex - 1] : null,
                            nextLesson: currentIndex < allLessons.length - 1 ? allLessons[currentIndex + 1] : null,
                            currentLesson: currentIndex >= 0 ? allLessons[currentIndex] : null
                        };
                    }

                    /**
                     * Actualizar estado de botones de navegación (VERSIÓN UNIFICADA)
                     */
                    function updateNavigationButtons() {
                        const navInfo = getNavigationInfo();
                        const prevBtn = document.getElementById('prevBtn');
                        const nextBtn = document.getElementById('nextBtn');

                        if (prevBtn) {
                            const canNavigatePrev = navInfo.hasPrevious;
                            prevBtn.disabled = !canNavigatePrev;
                            prevBtn.style.opacity = !canNavigatePrev ? '0.5' : '1';
                            prevBtn.title = !navInfo.hasPrevious ? 'First lesson' : navInfo.previousLesson?.title;
                        }

                        if (nextBtn) {
                            // For admins, don't check if lesson is locked
                            const isNextLocked = ASG_CONFIG.IS_ADMIN ? false : isLessonLocked(navInfo.nextLesson?.id);
                            const canNavigateNext = navInfo.hasNext && !isNextLocked;
                            nextBtn.disabled = !canNavigateNext;
                            nextBtn.style.opacity = !canNavigateNext ? '0.5' : '1';
                            nextBtn.title = !navInfo.hasNext ? 'Last lesson' :
                                !canNavigateNext ? (ASG_CONFIG.IS_ADMIN ? 'No next lesson' : 'Complete this lesson to continue') :
                                navInfo.nextLesson?.title;
                        }

                        // Configure admin-specific navigation
                        configureAdminNavigation();

                        console.log('🔄 Navigation buttons updated:', {
                            hasPrevious: navInfo.hasPrevious,
                            hasNext: navInfo.hasNext,
                            nextLessonId: navInfo.nextLesson?.id,
                            isNextLocked: navInfo.nextLesson ? (ASG_CONFIG.IS_ADMIN ? 'Admin - Always Unlocked' : isLessonLocked(navInfo.nextLesson.id)) : 'N/A'
                        });
                    }

                    // ===== FUNCIONES AUXILIARES =====

                    /**
                     * Mostrar mensaje de error
                     */
                    function showError(message) {
                        const container = document.getElementById('lessonContentArea');
                        container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h3 class="mt-3">Error</h3>
                        <p class="text-white">${message}</p>
                        <button class="btn" style="background: #0C1B40; color: white;" onclick="loadCourseData()">
                            <i class="bi bi-arrow-clockwise me-2"></i>Retry
                        </button>
                    </div>
                `;
                    }

                    /**
                     * Iniciar quiz
                     */
                    function startQuiz() {
                        if (!asgState.currentQuiz || !asgState.currentQuiz.questions) {
                            showNotification('No hay datos de quiz disponibles', 'error');
                            return;
                        }

                        // Reset quiz state
                        asgState.quizAnswers = [];
                        asgState.currentQuestionIndex = 0;
                        asgState.quizScore = 0;
                        asgState.quizValidation = {}; // Track which questions are answered correctly

                        // Disable complete button while quiz is in progress
                        disableCompleteButtonForQuiz();

                        // Hide intro and show quiz content
                        document.getElementById('quizIntro').style.display = 'none';
                        document.getElementById('quizContent').style.display = 'block';
                        document.getElementById('quizResults').style.display = 'none';

                        // Render first question
                        renderQuizQuestion();
                    }

                    /**
                     * Renderizar pregunta actual del quiz
                     */
                    function renderQuizQuestion() {
                        const question = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                        const totalQuestions = asgState.currentQuiz.questions.length;
                        const progress = ((asgState.currentQuestionIndex + 1) / totalQuestions) * 100;

                        const quizContent = document.getElementById('quizContent');

                        let optionsHtml = '';
                        let instructionText = '';

                        if (question.type === 'multiple_choice') {
                            instructionText = question.allow_multiple ? 'Select all that apply:' : 'Select one option:';
                            const inputType = question.allow_multiple ? 'checkbox' : 'radio';

                            optionsHtml = question.options.map((option, index) => `
                        <div class="quiz-option" onclick="selectQuizOption(${index}, ${question.allow_multiple || false})">
                            <input type="${inputType}" name="question_${question.id}" value="${index}" style="pointer-events: none;">
                            <span class="option-text">${option}</span>
                        </div>
                    `).join('');
                        } else if (question.type === 'true_false') {
                            instructionText = 'Select True or False:';
                            optionsHtml = `
                        <div class="quiz-option" onclick="selectQuizOption(true, false)">
                            <input type="radio" name="question_${question.id}" value="true" style="pointer-events: none;">
                            <span class="option-text">True</span>
                        </div>
                        <div class="quiz-option" onclick="selectQuizOption(false, false)">
                            <input type="radio" name="question_${question.id}" value="false" style="pointer-events: none;">
                            <span class="option-text">False</span>
                        </div>
                    `;
                        } else if (question.type === 'subjective') {
                            instructionText = 'Write your answer:';
                            optionsHtml = `
                        <div class="subjective-answer">
                            <textarea class="form-control" id="subjective_answer" rows="4"
                                      placeholder="Type your answer here..."></textarea>
                        </div>
                    `;
                        } else if (question.type === 'text_field') {
                            instructionText = 'Write your answer:';
                            optionsHtml = `
                        <div class="subjective-answer">
                            <textarea class="form-control" id="subjective_answer" rows="3"
                                      placeholder="Type your answer here..."></textarea>
                        </div>
                    `;
                        }

                        quizContent.innerHTML = `
                    <div class="quiz-header">
                        <div class="quiz-progress">
                            <div class="progress-bar" style="width: ${progress}%"></div>
                        </div>
                        <div class="question-counter">
                            Question ${asgState.currentQuestionIndex + 1} of ${totalQuestions}
                        </div>
                    </div>

                    <div class="quiz-question-content">
                        <h4 class="question-text">${question.question}</h4>
                        <p class="instruction-text">${instructionText}</p>

                        <div class="quiz-options">
                            ${optionsHtml}
                        </div>
                    </div>

                    <div class="quiz-navigation">
                        <button class="btn btn-outline-secondary" onclick="previousQuestion()"
                                ${asgState.currentQuestionIndex === 0 ? 'disabled' : ''}>
                            <i class="bi bi-arrow-left me-2"></i>Previous
                        </button>

                        <button class="btn btn-next-question" style="background: #0C1B40; color: white;" onclick="nextQuestion()" id="nextQuestionBtn">
                            ${asgState.currentQuestionIndex === totalQuestions - 1 ? 'Finish Exercise' : 'Next'}
                            <i class="bi bi-arrow-right ms-2"></i>
                        </button>
                    </div>
                `;

                        // Restore previous answer if exists
                        const previousAnswer = asgState.quizAnswers[asgState.currentQuestionIndex];
                        if (previousAnswer !== undefined) {
                            restorePreviousAnswer(previousAnswer, question);
                        }

                        // Initialize navigation state
                        setTimeout(() => {
                            updateQuizNavigationButtons();
                        }, 100);
                    }

                    // ===== SISTEMA DE MONITOREO CONTINUO =====

                    /**
                     * Monitoreo continuo de acceso (verificación cada 5 minutos)
                     */
                    function startAccessMonitoring() {
                        console.log('🔍 Starting continuous access monitoring...');

                        setInterval(async () => {
                            console.log('🔄 Performing periodic access check...');

                            const hasAccess = await verifyAccessBeforeLoad();
                            if (!hasAccess) {
                                console.log('🚨 SECURITY ALERT: Access lost during session');
                                // El usuario perdió acceso durante la sesión
                                showNotification('Your access to this course has expired. Please refresh the page.', 'warning', 10000);
                            }
                        }, 5 * 60 * 1000); // Cada 5 minutos
                    }

                    /**
                     * Verificación adicional en eventos de ventana
                     */
                    function setupSecurityEventListeners() {
                        // Verificar cuando la ventana vuelve a tener foco
                        window.addEventListener('focus', async () => {
                            console.log('🔍 Window focused - checking access...');
                            const hasAccess = await verifyAccessBeforeLoad();
                            if (!hasAccess) {
                                console.log('🚨 Access lost - blocking content');
                            }
                        });

                        // Verificar antes de navegar a otra lección
                        window.addEventListener('beforeunload', () => {
                            console.log('🔄 Page unloading - access monitoring stopped');
                        });
                    }

                    // ===== INICIALIZACIÓN =====

                    /**
                     * Inicializar la aplicación cuando el DOM esté listo con Verificación Robusta
                     */
                    document.addEventListener('DOMContentLoaded', async function() {


                        // Verificar configuración básica
                        if (!ASG_CONFIG.COURSE_CODE) {
                            console.error('COURSE_CODE no definido después de fallbacks');
                            console.log('Parámetros URL disponibles:', new URLSearchParams(window.location.search));
                            showError('Código de curso no especificado en la URL');
                            return;
                        }

                        try {
                            // VERIFICACIÓN INICIAL ROBUSTA


                            // Inicializar eventos del nuevo diseño
                            initializeNewDesignEvents();

                            // Configure admin navigation if user is admin
                            configureAdminNavigation();

                            // Configurar listeners de seguridad
                            setupSecurityEventListeners();

                            // Actualizar información del usuario
                            await updateUserInfo();

                            // Cargar datos del curso (incluye verificación robusta automática)

                            await loadCourseData();

                            // Configurar navegación del navegador con verificación
                            window.addEventListener('popstate', async function() {
                                const lessonId = getCurrentLessonId();
                                if (lessonId) {
                                    // Verificar acceso antes de cargar nueva lección
                                    const hasAccess = await verifyAccessBeforeLoad();
                                    if (hasAccess) {
                                        loadLessonContent(lessonId);
                                    }
                                }
                            });

                            // Iniciar monitoreo continuo de seguridad
                            startAccessMonitoring();



                        } catch (error) {
                            console.error('❌ Error durante inicialización con seguridad robusta:', error);
                            showNotification('Error loading course with security verification', 'error');
                        }
                    });

                    /**
                     * Actualizar información del usuario en el header
                     */
                    async function updateUserInfo() {
                        const username = document.getElementById('username');
                        if (username && ASG_CONFIG.USER_ID) {
                            try {
                                // Obtener información del usuario desde WordPress
                                const response = await fetch(`${ASG_CONFIG.API_BASE.replace('/asg/v1', '')}/wp/v2/users/${ASG_CONFIG.USER_ID}`, {
                                    headers: {
                                        'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                                    }
                                });

                                if (response.ok) {
                                    const userData = await response.json();
                                    const displayName = userData.name || userData.display_name || userData.username || 'User';
                                    username.textContent = displayName;
                                    console.log('✅ Username updated to:', displayName);
                                } else {
                                    // Fallback: usar información de PHP si está disponible
                                    username.textContent = '<?php echo wp_get_current_user()->display_name ?: "User"; ?>';
                                }
                            } catch (error) {
                                console.error('❌ Error loading user info:', error);
                                // Fallback: usar información de PHP
                                username.textContent = '<?php echo wp_get_current_user()->display_name ?: "User"; ?>';
                            }
                        }
                    }

                    /**
                     * Seleccionar opción de quiz con validación inmediata
                     */
                    function selectQuizOption(value, isMultiple = false) {
                        const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                        const currentOption = event.currentTarget;

                        // Clear previous feedback
                        clearQuizFeedback();

                        if (isMultiple) {
                            // Handle multiple selection (checkboxes)
                            const checkbox = currentOption.querySelector('input[type="checkbox"]');
                            checkbox.checked = !checkbox.checked;

                            // Update visual selection
                            if (checkbox.checked) {
                                currentOption.classList.add('selected');
                            } else {
                                currentOption.classList.remove('selected');
                            }

                            // Update answers array
                            let currentAnswers = asgState.quizAnswers[asgState.currentQuestionIndex] || [];
                            if (checkbox.checked) {
                                if (!currentAnswers.includes(value)) {
                                    currentAnswers.push(value);
                                }
                            } else {
                                currentAnswers = currentAnswers.filter(answer => answer !== value);
                            }
                            asgState.quizAnswers[asgState.currentQuestionIndex] = currentAnswers;

                            // Validate multiple choice answer
                            validateMultipleChoiceAnswer(currentAnswers, currentQuestion);

                        } else {
                            // Handle single selection (radio buttons)
                            const radio = currentOption.querySelector('input[type="radio"]');
                            radio.checked = true;

                            // Remove selection and feedback from other options
                            document.querySelectorAll('.quiz-option').forEach(option => {
                                option.classList.remove('selected', 'correct', 'incorrect');
                            });

                            // Add selection to current option
                            currentOption.classList.add('selected');

                            // Update answer
                            asgState.quizAnswers[asgState.currentQuestionIndex] = value;

                            // Validate single choice answer immediately
                            validateSingleChoiceAnswer(value, currentQuestion, currentOption);
                        }
                    }

                    /**
                     * Validar respuesta de selección única
                     */
                    function validateSingleChoiceAnswer(selectedValue, question, optionElement) {
                        const correctAnswer = question.correct_answer;

                        // Debug logging
                        console.log('🔍 Validating answer:', {
                            selectedValue: selectedValue,
                            selectedValueType: typeof selectedValue,
                            correctAnswer: correctAnswer,
                            correctAnswerType: typeof correctAnswer,
                            isArray: Array.isArray(correctAnswer)
                        });

                        // Handle array format (new format)
                        let isCorrect = false;
                        if (Array.isArray(correctAnswer)) {
                            // For true/false questions, convert selectedValue to boolean for comparison
                            if (question.type === 'true_false') {
                                const selectedBool = selectedValue === 'true' || selectedValue === true;
                                const correctBool = correctAnswer[0] === true || correctAnswer[0] === 'true';
                                isCorrect = selectedBool === correctBool;

                                console.log('🔍 True/False validation:', {
                                    selectedBool: selectedBool,
                                    correctBool: correctBool,
                                    isCorrect: isCorrect
                                });
                            } else {
                                // For other question types, check if selectedValue is in the array
                                isCorrect = correctAnswer.includes(selectedValue) ||
                                    correctAnswer.includes(parseInt(selectedValue)) ||
                                    correctAnswer.includes(selectedValue.toString());
                            }
                        } else {
                            // Handle legacy format (direct comparison)
                            isCorrect = selectedValue == correctAnswer;
                        }

                        // Clear previous states
                        document.querySelectorAll('.quiz-option').forEach(opt => {
                            opt.classList.remove('correct', 'incorrect');
                        });

                        if (isCorrect) {
                            // Correct answer
                            optionElement.classList.add('correct');
                            showQuizFeedback('correct', '✅ Correct! Well done.');

                            // Disable other options
                            document.querySelectorAll('.quiz-option').forEach(opt => {
                                if (opt !== optionElement) {
                                    opt.classList.add('disabled');
                                }
                            });

                            // Store that this question is answered correctly
                            asgState.quizValidation = asgState.quizValidation || {};
                            asgState.quizValidation[asgState.currentQuestionIndex] = true;

                            // Enable next button after a short delay
                            setTimeout(() => {
                                updateQuizNavigationButtons();
                            }, 1000);

                        } else {
                            // Incorrect answer
                            optionElement.classList.add('incorrect');
                            showQuizFeedback('incorrect', '❌ Incorrect. Please try again.');

                            // Store that this question is not answered correctly
                            asgState.quizValidation = asgState.quizValidation || {};
                            asgState.quizValidation[asgState.currentQuestionIndex] = false;

                            // Keep other options enabled for retry
                            setTimeout(() => {
                                optionElement.classList.remove('incorrect');
                                optionElement.classList.remove('selected');
                                const radio = optionElement.querySelector('input[type="radio"]');
                                if (radio) radio.checked = false;

                                // Clear the wrong answer
                                delete asgState.quizAnswers[asgState.currentQuestionIndex];

                                updateQuizNavigationButtons();
                            }, 1500);
                        }
                    }

                    /**
                     * Validar respuesta de selección múltiple
                     */
                    function validateMultipleChoiceAnswer(selectedValues, question) {
                        const correctAnswers = Array.isArray(question.correct_answer) ?
                            question.correct_answer : [question.correct_answer];

                        // Sort arrays for comparison
                        const sortedSelected = [...selectedValues].sort();
                        const sortedCorrect = [...correctAnswers].sort();

                        const isCorrect = JSON.stringify(sortedSelected) === JSON.stringify(sortedCorrect);

                        if (selectedValues.length === 0) {
                            // No selection yet
                            asgState.quizValidation = asgState.quizValidation || {};
                            asgState.quizValidation[asgState.currentQuestionIndex] = false;
                            updateQuizNavigationButtons();
                            return;
                        }

                        if (isCorrect) {
                            // All correct answers selected
                            showQuizFeedback('correct', '✅ Correct! All right answers selected.');

                            // Mark all selected options as correct
                            selectedValues.forEach(value => {
                                const option = document.querySelector(`input[value="${value}"]`);
                                if (option) {
                                    option.closest('.quiz-option').classList.add('correct');
                                }
                            });

                            // Disable all options
                            document.querySelectorAll('.quiz-option').forEach(opt => {
                                opt.classList.add('disabled');
                            });

                            asgState.quizValidation = asgState.quizValidation || {};
                            asgState.quizValidation[asgState.currentQuestionIndex] = true;

                            setTimeout(() => {
                                updateQuizNavigationButtons();
                            }, 1000);

                        } else {
                            // Partial or incorrect selection
                            showQuizFeedback('incorrect', '❌ Not quite right. Keep trying!');

                            asgState.quizValidation = asgState.quizValidation || {};
                            asgState.quizValidation[asgState.currentQuestionIndex] = false;
                            updateQuizNavigationButtons();
                        }
                    }

                    /**
                     * Mostrar feedback del quiz
                     */
                    function showQuizFeedback(type, message) {
                        // Remove existing feedback
                        const existingFeedback = document.querySelector('.quiz-feedback');
                        if (existingFeedback) {
                            existingFeedback.remove();
                        }

                        // Create new feedback element
                        const feedback = document.createElement('div');
                        feedback.className = `quiz-feedback ${type}`;
                        feedback.innerHTML = `<i class="bi bi-${type === 'correct' ? 'check-circle' : 'x-circle'} me-2"></i>${message}`;
                        feedback.style.display = 'block';

                        // Insert after quiz options
                        const quizOptions = document.querySelector('.quiz-options');
                        if (quizOptions) {
                            quizOptions.parentNode.insertBefore(feedback, quizOptions.nextSibling);
                        }
                    }

                    /**
                     * Limpiar feedback del quiz
                     */
                    function clearQuizFeedback() {
                        const feedback = document.querySelector('.quiz-feedback');
                        if (feedback) {
                            feedback.remove();
                        }
                    }

                    /**
                     * Actualizar botones de navegación del quiz
                     */
                    function updateQuizNavigationButtons() {
                        const nextBtn = document.querySelector('.btn-next-question');
                        const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];

                        if (!nextBtn) return;

                        // Check if current question is answered correctly
                        const isAnsweredCorrectly = asgState.quizValidation &&
                            asgState.quizValidation[asgState.currentQuestionIndex] === true;

                        // For subjective and text_field questions, always allow navigation
                        const isSubjective = currentQuestion.type === 'subjective' || currentQuestion.type === 'text_field';

                        if (isAnsweredCorrectly || isSubjective) {
                            nextBtn.disabled = false;
                            nextBtn.style.opacity = '1';
                            nextBtn.style.cursor = 'pointer';
                            nextBtn.title = 'Continue to next question';
                        } else {
                            nextBtn.disabled = true;
                            nextBtn.style.opacity = '0.5';
                            nextBtn.style.cursor = 'not-allowed';
                            nextBtn.title = 'Answer correctly to continue';
                        }
                    }

                    /**
                     * Navegar a pregunta siguiente (con validación)
                     */
                    function nextQuestion() {
                        const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];

                        // Save current answer for subjective and text_field questions
                        if (currentQuestion.type === 'subjective' || currentQuestion.type === 'text_field') {
                            const textarea = document.getElementById('subjective_answer');
                            if (textarea) {
                                asgState.quizAnswers[asgState.currentQuestionIndex] = textarea.value;
                            }
                        }

                        // Check if question is answered correctly (except for subjective and text_field)
                        const isAnsweredCorrectly = asgState.quizValidation &&
                            asgState.quizValidation[asgState.currentQuestionIndex] === true;
                        const isSubjective = currentQuestion.type === 'subjective' || currentQuestion.type === 'text_field';

                        if (!isAnsweredCorrectly && !isSubjective) {
                            showNotification('⚠️ Please answer the question correctly before continuing.', 'warning');
                            return;
                        }

                        // Clear feedback before moving
                        clearQuizFeedback();

                        if (asgState.currentQuestionIndex < asgState.currentQuiz.questions.length - 1) {
                            asgState.currentQuestionIndex++;
                            renderQuizQuestion();
                        } else {
                            finishQuiz();
                        }
                    }

                    /**
                     * Navegar a pregunta anterior
                     */
                    function previousQuestion() {
                        if (asgState.currentQuestionIndex > 0) {
                            asgState.currentQuestionIndex--;
                            renderQuizQuestion();
                        }
                    }

                    /**
                     * Restaurar respuesta anterior
                     */
                    function restorePreviousAnswer(previousAnswer, question) {
                        // Check if this question was answered correctly before
                        const wasAnsweredCorrectly = asgState.quizValidation &&
                            asgState.quizValidation[asgState.currentQuestionIndex] === true;

                        if (question.type === 'multiple_choice' && question.allow_multiple && Array.isArray(previousAnswer)) {
                            // Restore multiple selections
                            previousAnswer.forEach(value => {
                                const option = document.querySelector(`input[value="${value}"]`);
                                if (option) {
                                    option.checked = true;
                                    const optionElement = option.closest('.quiz-option');
                                    optionElement.classList.add('selected');

                                    // If was answered correctly, show correct state
                                    if (wasAnsweredCorrectly) {
                                        optionElement.classList.add('correct');
                                    }
                                }
                            });

                            // If answered correctly, disable all options and show feedback
                            if (wasAnsweredCorrectly) {
                                document.querySelectorAll('.quiz-option').forEach(opt => {
                                    opt.classList.add('disabled');
                                });
                                showQuizFeedback('correct', '✅ Correct! All right answers selected.');
                            }

                        } else if (question.type === 'subjective' || question.type === 'text_field') {
                            // Restore subjective/text_field answer
                            const textarea = document.getElementById('subjective_answer');
                            if (textarea && previousAnswer) {
                                textarea.value = previousAnswer;
                            }
                        } else {
                            // Restore single selection
                            const option = document.querySelector(`input[value="${previousAnswer}"]`);
                            if (option) {
                                option.checked = true;
                                const optionElement = option.closest('.quiz-option');
                                optionElement.classList.add('selected');

                                // If was answered correctly, show correct state
                                if (wasAnsweredCorrectly) {
                                    optionElement.classList.add('correct');

                                    // Disable other options
                                    document.querySelectorAll('.quiz-option').forEach(opt => {
                                        if (opt !== optionElement) {
                                            opt.classList.add('disabled');
                                        }
                                    });

                                    showQuizFeedback('correct', '✅ Correct! Well done.');
                                }
                            }
                        }
                    }

                    // Hacer funciones disponibles globalmente para onclick
                    window.navigateToLesson = navigateToLesson;
                    window.navigateToPrevious = navigateToPrevious;
                    window.navigateToNext = navigateToNext;
                    window.markLessonComplete = markLessonComplete;
                    window.startQuiz = startQuiz;
                    window.loadCourseData = loadCourseData;


                    window.toggleModule = toggleModule;


                    /**
                     * Finalizar quiz y mostrar resultados
                     */
                    async function finishQuiz() {
                        try {
                            console.log('Finalizando quiz...');

                            // Save current answer for subjective and text_field questions
                            const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                            if (currentQuestion.type === 'subjective' || currentQuestion.type === 'text_field') {
                                const textarea = document.getElementById('subjective_answer');
                                if (textarea) {
                                    asgState.quizAnswers[asgState.currentQuestionIndex] = textarea.value;
                                }
                            }

                            // Calculate score
                            const scoreData = calculateQuizScore();
                            const passed = scoreData.score >= (asgState.currentQuiz.passing_score || 70);

                            // Show results
                            showQuizResults(scoreData, passed);

                            // Handle quiz completion based on pass/fail
                            if (passed) {
                                // Enable the complete button immediately
                                enableCompleteButtonForQuiz();

                            } else {
                                // Keep complete button disabled if quiz failed
                                keepCompleteButtonDisabledForFailedQuiz();
                            }

                        } catch (error) {
                            console.error('Error finishing quiz:', error);
                            showNotification('Error al finalizar el quiz', 'error');
                        }
                    }

                    /**
                     * Calcular puntuación del quiz
                     */
                    function calculateQuizScore() {
                        let correctAnswers = 0;
                        let totalQuestions = asgState.currentQuiz.questions.length;
                        let subjectiveAnswers = 0;
                        let totalPoints = 0;
                        let earnedPoints = 0;

                        asgState.currentQuiz.questions.forEach((question, index) => {
                            const userAnswer = asgState.quizAnswers[index];
                            const questionPoints = question.points || 25;
                            totalPoints += questionPoints;

                            if (question.type === 'subjective' || question.type === 'text_field') {
                                // Subjective and text_field questions are auto-passed if answered
                                if (userAnswer && userAnswer.trim().length > 0) {
                                    correctAnswers++;
                                    earnedPoints += questionPoints;
                                    subjectiveAnswers++;
                                }
                            } else if (question.type === 'multiple_choice' && question.allow_multiple) {
                                // Multiple selection questions
                                const correctAnswerSet = new Set(question.correct_answer);
                                const userAnswerSet = new Set(Array.isArray(userAnswer) ? userAnswer : []);

                                if (correctAnswerSet.size === userAnswerSet.size && [...correctAnswerSet].every(answer => userAnswerSet.has(answer))) {
                                    correctAnswers++;
                                    earnedPoints += questionPoints;
                                }
                            } else {
                                // Single selection questions
                                if (Array.isArray(question.correct_answer)) {
                                    // Special handling for true/false questions
                                    if (question.type === 'true_false') {
                                        const userBool = userAnswer === 'true' || userAnswer === true;
                                        const correctBool = question.correct_answer[0] === true || question.correct_answer[0] === 'true';
                                        if (userBool === correctBool) {
                                            correctAnswers++;
                                            earnedPoints += questionPoints;
                                        }
                                    } else {
                                        // For other question types, check if userAnswer is in the array
                                        if (question.correct_answer.includes(userAnswer) ||
                                            question.correct_answer.includes(parseInt(userAnswer)) ||
                                            question.correct_answer.includes(userAnswer.toString())) {
                                            correctAnswers++;
                                            earnedPoints += questionPoints;
                                        }
                                    }
                                } else {
                                    if (question.correct_answer === userAnswer) {
                                        correctAnswers++;
                                        earnedPoints += questionPoints;
                                    }
                                }
                            }
                        });

                        const score = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;

                        return {
                            score: score,
                            correct_answers: correctAnswers,
                            total_questions: totalQuestions,
                            subjective_answers: subjectiveAnswers,
                            earned_points: earnedPoints,
                            total_points: totalPoints
                        };
                    }

                    /**
                     * Hide complete button for quiz lessons (should not be visible at all)
                     */
                    function hideCompleteButtonForQuiz() {
                        const btnMarkComplete = document.getElementById('btnMarkComplete');

                        if (!btnMarkComplete) {
                            console.warn('⚠️ btnMarkComplete not found in hideCompleteButtonForQuiz');
                            return;
                        }

                        // Hide the complete button completely for quiz lessons
                        btnMarkComplete.style.display = 'none';
                        console.log('🚫 Complete button hidden for quiz lesson');
                    }

                    /**
                     * Disable complete button when quiz starts
                     */
                    function disableCompleteButtonForQuiz() {
                        const btnMarkComplete = document.getElementById('btnMarkComplete');

                        if (!btnMarkComplete) {
                            console.warn('⚠️ btnMarkComplete not found in disableCompleteButtonForQuiz');
                            return;
                        }

                        // Get current lesson ID
                        const currentLessonId = getCurrentLessonId();
                        if (!currentLessonId) {
                            console.warn('⚠️ Could not get current lesson ID');
                            return;
                        }

                        // Check if lesson is already completed
                        const completedLessons = asgState.courseProgress?.completed_lessons_list || [];
                        const isAlreadyCompleted = completedLessons.includes(parseInt(currentLessonId));

                        if (isAlreadyCompleted) {
                            console.log('✅ Lesson already completed, keeping completed state');
                            return;
                        }

                        // Disable the complete button during quiz
                        btnMarkComplete.style.display = 'flex';
                        btnMarkComplete.innerHTML = '<i class="bi bi-clock"></i> Complete Quiz First';
                        btnMarkComplete.disabled = true;
                        btnMarkComplete.classList.remove('completed');
                        btnMarkComplete.style.background = '#6c757d';
                        btnMarkComplete.style.color = 'white';
                        btnMarkComplete.onclick = null;

                        console.log('🔒 Complete button disabled during quiz');
                    }

                    /**
                     * Keep complete button disabled when quiz is failed
                     */
                    function keepCompleteButtonDisabledForFailedQuiz() {
                        const btnMarkComplete = document.getElementById('btnMarkComplete');

                        if (!btnMarkComplete) {
                            console.warn('⚠️ btnMarkComplete not found in keepCompleteButtonDisabledForFailedQuiz');
                            return;
                        }

                        // Get current lesson ID
                        const currentLessonId = getCurrentLessonId();
                        if (!currentLessonId) {
                            console.warn('⚠️ Could not get current lesson ID');
                            return;
                        }

                        // Check if lesson is already completed
                        const completedLessons = asgState.courseProgress?.completed_lessons_list || [];
                        const isAlreadyCompleted = completedLessons.includes(parseInt(currentLessonId));

                        if (isAlreadyCompleted) {
                            console.log('✅ Lesson already completed, keeping completed state');
                            return;
                        }

                        // Keep the complete button disabled for failed quiz
                        btnMarkComplete.style.display = 'flex';
                        btnMarkComplete.innerHTML = '<i class="bi bi-x-circle"></i> Pass Quiz to Complete';
                        btnMarkComplete.disabled = true;
                        btnMarkComplete.classList.remove('completed');
                        btnMarkComplete.style.background = '#dc3545';
                        btnMarkComplete.style.color = 'white';
                        btnMarkComplete.onclick = null;

                        console.log('❌ Complete button kept disabled after quiz failure');
                    }

                    /**
                     * Enable complete button immediately when quiz is passed (but keep it hidden)
                     */
                    function enableCompleteButtonForQuiz() {
                        const btnMarkComplete = document.getElementById('btnMarkComplete');

                        if (!btnMarkComplete) {
                            console.warn('⚠️ btnMarkComplete not found in enableCompleteButtonForQuiz');
                            return;
                        }

                        // Get current lesson ID
                        const currentLessonId = getCurrentLessonId();
                        if (!currentLessonId) {
                            console.warn('⚠️ Could not get current lesson ID');
                            return;
                        }

                        // Check if lesson is already completed
                        const completedLessons = asgState.courseProgress?.completed_lessons_list || [];
                        const isAlreadyCompleted = completedLessons.includes(parseInt(currentLessonId));

                        if (isAlreadyCompleted) {
                            console.log('✅ Lesson already completed, keeping completed state');
                            return;
                        }

                        // Enable the complete button but keep it HIDDEN
                        // The Continue button will trigger it automatically
                        btnMarkComplete.style.display = 'none'; // Keep hidden
                        btnMarkComplete.innerHTML = '<i class="bi bi-check-circle"></i> Mark as Complete';
                        btnMarkComplete.disabled = false;
                        btnMarkComplete.classList.remove('completed');
                        btnMarkComplete.style.background = '#28a745';
                        btnMarkComplete.style.color = 'white';
                        btnMarkComplete.onclick = () => {
                            console.log('🎯 Complete button clicked after quiz completion for lesson:', currentLessonId);
                            markLessonComplete(currentLessonId);
                        };

                        console.log('✅ Complete button enabled after quiz completion (but kept hidden)');
                    }

                    /**
                     * Mostrar resultados del quiz
                     */
                    function showQuizResults(scoreData, passed) {
                        const passingScore = asgState.currentQuiz.passing_score || 70;

                        document.getElementById('quizContent').style.display = 'none';
                        const resultsContainer = document.getElementById('quizResults');
                        resultsContainer.style.display = 'block';

                        resultsContainer.innerHTML = `
                    <div class="quiz-results-container">
                        <div class="results-header ${passed ? 'passed' : 'failed'}">
                            <div class="score-circle">
                                <div class="score-number">${scoreData.score}%</div>
                            </div>
                            <h3>${passed ? '🎉 Congratulations!' : '😔 Try Again'}</h3>
                            <p>${passed ? 'You passed the exercise!' : `Please, try again.`}</p>
                        </div>

                        <div class="results-details">
                            <div class="detail-item">
                                <span class="label">Correct Answers:</span>
                                <span class="value">${scoreData.correct_answers}/${scoreData.total_questions}</span>
                            </div>
                            <div class="detail-item">
                              
                            </div>
                            <div class="detail-item">
                               
                            </div>
                            ${scoreData.subjective_answers > 0 ? `
                                <div class="detail-item">
                                    <span class="label">Subjective Questions:</span>
                                    <span class="value">${scoreData.subjective_answers}</span>
                                </div>
                            ` : ''}
                        </div>

                        <div class="results-actions">
                            ${!passed ? `
                                <button class="btn btn-warning" onclick="retakeQuiz()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Retake Quiz
                                </button>
                            ` : ''}
                            <button class="btn" style="background: #0C1B40; color: white;" onclick="continueToNextLesson()">
                                <i class="bi bi-arrow-right me-2"></i>Continue
                            </button>
                        </div>
                    </div>
                `;
                    }

                    /**
                     * Reintentar quiz
                     */
                    function retakeQuiz() {
                        document.getElementById('quizResults').style.display = 'none';
                        startQuiz();
                    }

                    /**
                     * Continuar a siguiente lección (después de completar quiz)
                     */
                    function continueToNextLesson() {
                        // First, automatically click the hidden "Mark as Complete" button
                        const btnMarkComplete = document.getElementById('btnMarkComplete');

                        if (btnMarkComplete && !btnMarkComplete.disabled) {
                            console.log('🎯 Auto-clicking Mark as Complete button before navigation');

                            // Trigger the onclick function directly
                            if (btnMarkComplete.onclick) {
                                btnMarkComplete.onclick();
                            }

                            // Wait a moment for the completion to process, then navigate
                            setTimeout(() => {
                                navigateToNext();
                            }, 500);
                        } else {
                            // If button is not available or disabled, just navigate
                            console.log('⚠️ Mark as Complete button not available, navigating directly');
                            navigateToNext();
                        }
                    }

                    /**
                     * Navegar a pregunta anterior del quiz
                     */
                    function previousQuestion() {
                        if (asgState.currentQuestionIndex > 0) {
                            // Save current answer before moving
                            saveCurrentAnswer();
                            asgState.currentQuestionIndex--;
                            renderQuizQuestion();
                        }
                    }

                    /**
                     * Navegar a siguiente pregunta del quiz
                     */
                    function nextQuestion() {
                        // Save current answer
                        saveCurrentAnswer();

                        if (asgState.currentQuestionIndex < asgState.currentQuiz.questions.length - 1) {
                            asgState.currentQuestionIndex++;
                            renderQuizQuestion();
                        } else {
                            // Last question, finish quiz
                            finishQuiz();
                        }
                    }

                    /**
                     * Guardar respuesta actual
                     */
                    function saveCurrentAnswer() {
                        const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];

                        if (currentQuestion.type === 'multiple_choice') {
                            if (currentQuestion.allow_multiple) {
                                // Multiple selection
                                const selectedOptions = [];
                                document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                                    selectedOptions.push(parseInt(checkbox.value));
                                });
                                asgState.quizAnswers[asgState.currentQuestionIndex] = selectedOptions;
                            } else {
                                // Single selection
                                const selectedOption = document.querySelector('input[type="radio"]:checked');
                                if (selectedOption) {
                                    asgState.quizAnswers[asgState.currentQuestionIndex] = parseInt(selectedOption.value);
                                }
                            }
                        } else if (currentQuestion.type === 'true_false') {
                            const selectedOption = document.querySelector('input[type="radio"]:checked');
                            if (selectedOption) {
                                asgState.quizAnswers[asgState.currentQuestionIndex] = selectedOption.value === 'true';
                            }
                        } else if (currentQuestion.type === 'subjective' || currentQuestion.type === 'text_field') {
                            const textarea = document.getElementById('subjective_answer');
                            if (textarea) {
                                asgState.quizAnswers[asgState.currentQuestionIndex] = textarea.value;
                            }
                        }
                    }

                    /**
                     * Restaurar respuesta anterior
                     */
                    function restorePreviousAnswer(previousAnswer, question) {
                        if (question.type === 'multiple_choice' && question.allow_multiple && Array.isArray(previousAnswer)) {
                            // Restore multiple selections
                            previousAnswer.forEach(value => {
                                const option = document.querySelector(`input[value="${value}"]`);
                                if (option) {
                                    option.checked = true;
                                    option.closest('.quiz-option').classList.add('selected');
                                }
                            });
                        } else if (question.type === 'subjective' || question.type === 'text_field') {
                            // Restore subjective/text_field answer
                            const textarea = document.getElementById('subjective_answer');
                            if (textarea && previousAnswer) {
                                textarea.value = previousAnswer;
                            }
                        } else {
                            // Restore single selection
                            const option = document.querySelector(`input[value="${previousAnswer}"]`);
                            if (option) {
                                option.checked = true;
                                option.closest('.quiz-option').classList.add('selected');
                            }
                        }
                    }



                    window.selectQuizOption = selectQuizOption;
                    window.showQuizFeedback = showQuizFeedback;
                    window.clearQuizFeedback = clearQuizFeedback;
                    window.updateQuizNavigationButtons = updateQuizNavigationButtons;
                    window.validateSingleChoiceAnswer = validateSingleChoiceAnswer;
                    window.validateMultipleChoiceAnswer = validateMultipleChoiceAnswer;




                    window.nextQuestion = nextQuestion;
                    window.previousQuestion = previousQuestion;
                    window.finishQuiz = finishQuiz;
                    window.retakeQuiz = retakeQuiz;
                    window.continueToNextLesson = continueToNextLesson;
                    window.findNextUnlockedLesson = findNextUnlockedLesson;
                    window.showCourseContentModal = showCourseContentModal;
                    window.closeCourseContentModal = closeCourseContentModal;

                    // Certificate modal functions
                    window.showCourseCompletionModal = showCourseCompletionModal;
                    window.populateCertificateData = populateCertificateData;
                    window.downloadCertificate = downloadCertificate;
                    window.shareAchievement = shareAchievement;
                    window.closeCourseCompletionModal = closeCourseCompletionModal;

                    // Scroll function
                    window.scrollToLessonTitle = scrollToLessonTitle;











                    /**
                     * Mostrar modal con contenido del curso
                     */
                    function showCourseContentModal() {
                        const modal = document.getElementById('courseContentModal');
                        const modalBody = document.getElementById('courseContentBody');

                        if (modal && modalBody) {
                            modal.style.display = 'flex';
                            renderCourseContent(modalBody);
                        }
                    }

                    /**
                     * Cerrar modal de contenido del curso
                     */
                    function closeCourseContentModal() {
                        const modal = document.getElementById('courseContentModal');
                        if (modal) {
                            modal.style.display = 'none';
                        }
                    }

                    // Make function globally available
                    window.closeCourseContentModal = closeCourseContentModal;

                    // Cerrar modal al hacer clic fuera de él
                    document.addEventListener('click', function(e) {
                        const modal = document.getElementById('courseContentModal');
                        if (e.target === modal) {
                            closeCourseContentModal();
                        }
                    });

                    /**
                     * Renderizar contenido del curso en el modal
                     */
                    function renderCourseContent(container) {
                        if (!asgState.currentCourse || !asgState.currentCourse.modules) {
                            container.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Course content not available
                        </div>
                    `;
                            return;
                        }

                        const completedLessons = asgState.courseProgress?.completed_lessons_list || [];

                        let contentHtml = '';

                        asgState.currentCourse.modules.forEach((module, moduleIndex) => {
                            const moduleId = `modal-module-${moduleIndex}`;

                            // Debug module data
                            console.log(`📁 Module ${moduleIndex} data:`, {
                                rawModule: module,
                                detectedTitle: module.title_module || module.title || module.module_title || module.name || module.module_name,
                                allTitleFields: {
                                    title_module: module.title_module,
                                    title: module.title,
                                    module_title: module.module_title,
                                    name: module.name,
                                    module_name: module.module_name
                                },
                                lessonsCount: module.lessons?.length || 0
                            });

                            let lessonsHtml = '';
                            if (module.lessons && module.lessons.length > 0) {
                                lessonsHtml = module.lessons.map((lesson, lessonIndex) => {
                                    // Try multiple possible field names for lesson ID and title
                                    const lessonId = parseInt(lesson.id_lesson || lesson.id || lesson.lesson_id);
                                    const lessonTitle = lesson.title_lesson || lesson.title || lesson.lesson_title || lesson.name || lesson.lesson_name || `Lesson ${lessonIndex + 1}`;

                                    console.log(`📄 Lesson ${lessonIndex} data:`, {
                                        rawLesson: lesson,
                                        detectedId: lessonId,
                                        detectedTitle: lessonTitle,
                                        allTitleFields: {
                                            title_lesson: lesson.title_lesson,
                                            title: lesson.title,
                                            lesson_title: lesson.lesson_title,
                                            name: lesson.name,
                                            lesson_name: lesson.lesson_name
                                        },
                                        allIdFields: {
                                            id_lesson: lesson.id_lesson,
                                            id: lesson.id,
                                            lesson_id: lesson.lesson_id
                                        }
                                    });

                                    const isCompleted = completedLessons.includes(lessonId);
                                    const isFirstLesson = moduleIndex === 0 && lessonIndex === 0;
                                    const isPreviousCompleted = isFirstLesson || completedLessons.includes(parseInt(module.lessons[lessonIndex - 1]?.id_lesson || module.lessons[lessonIndex - 1]?.id));
                                    const isUnlocked = isFirstLesson || isPreviousCompleted;

                                    const lessonType = lesson.type_lesson || lesson.lesson_type || lesson.type || 'video';
                                    const typeIcon = lessonType === 'video' ? 'play-circle' :
                                        lessonType === 'quiz' ? 'question-circle' : 'file-text';

                                    let statusIcon = '';
                                    let statusClass = '';

                                    if (isCompleted) {
                                        statusIcon = 'check-circle-fill';
                                        statusClass = 'completed';
                                    } else if (isUnlocked) {
                                        statusIcon = 'play-circle';
                                        statusClass = 'available';
                                    } else {
                                        statusIcon = 'lock';
                                        statusClass = 'locked';
                                    }

                                    return `
                                <div class="lesson-item" onclick="safeNavigateToLessonFromModal(${lessonId})" data-lesson-id="${lessonId}">
                                    <div class="lesson-icon">
                                        <i class="bi bi-${typeIcon}"></i>
                                    </div>
                                    <div class="lesson-info">
                                        <div class="lesson-name">${lessonTitle}</div>
                                        <div class="lesson-type">${lessonType} lesson</div>
                                    </div>
                                    <div class="lesson-status ${statusClass}">
                                        <i class="bi bi-${statusIcon}"></i>
                                    </div>
                                </div>
                            `;
                                }).join('');
                            }

                            const moduleTitle = module.title_module || module.title || module.module_title || module.name || module.module_name || `Module ${moduleIndex + 1}`;

                            contentHtml += `
                        <div class="course-module">
                            <div class="module-header" onclick="toggleModalModule('${moduleId}')">
                                <h4 class="module-title">${moduleTitle}</h4>
                                <i class="bi bi-chevron-right module-chevron" id="chevron-${moduleId}"></i>
                            </div>
                            <div class="module-lessons" id="${moduleId}">
                                ${lessonsHtml}
                            </div>
                        </div>
                    `;
                        });

                        container.innerHTML = contentHtml;

                        // Expandir el primer módulo por defecto
                        setTimeout(() => {
                            const firstModule = document.getElementById('modal-module-0');
                            const firstChevron = document.getElementById('chevron-modal-module-0');
                            if (firstModule && firstChevron) {
                                firstModule.classList.add('show');
                                firstChevron.classList.add('rotated');
                            }
                        }, 100);
                    }

                    /**
                     * Toggle módulo en el modal
                     */
                    function toggleModalModule(moduleId) {
                        const moduleContent = document.getElementById(moduleId);
                        const chevron = document.getElementById(`chevron-${moduleId}`);

                        console.log('🔄 Toggling module:', {
                            moduleId: moduleId,
                            moduleContent: !!moduleContent,
                            chevron: !!chevron,
                            isCurrentlyShown: moduleContent?.classList.contains('show')
                        });

                        if (moduleContent && chevron) {
                            const isShown = moduleContent.classList.contains('show');

                            if (isShown) {
                                moduleContent.classList.remove('show');
                                chevron.classList.remove('rotated');
                            } else {
                                moduleContent.classList.add('show');
                                chevron.classList.add('rotated');
                            }

                            console.log('✅ Module toggled:', {
                                moduleId: moduleId,
                                newState: moduleContent.classList.contains('show') ? 'expanded' : 'collapsed'
                            });
                        } else {
                            console.error('❌ Module elements not found:', {
                                moduleId: moduleId,
                                moduleContent: !!moduleContent,
                                chevron: !!chevron
                            });
                        }
                    }

                    // Hacer la función global para que funcione con onclick
                    window.toggleModalModule = toggleModalModule;

                    /**
                     * Navegar a lección desde el modal
                     */
                    function navigateToLessonFromModal(lessonId) {
                        closeCourseContentModal();
                        navigateToLesson(lessonId);
                    }

                    // Make function globally available
                    window.navigateToLessonFromModal = navigateToLessonFromModal;

                    // Ensure functions are available immediately
                    if (typeof window.navigateToLessonFromModal === 'undefined') {
                        console.error('❌ navigateToLessonFromModal not properly defined');
                    } else {
                        console.log('✅ navigateToLessonFromModal is available globally');
                    }

                    // Fallback function in case of timing issues
                    window.safeNavigateToLessonFromModal = function(lessonId) {
                        console.log('🔄 Safe navigation to lesson:', lessonId);

                        if (typeof window.closeCourseContentModal === 'function') {
                            window.closeCourseContentModal();
                        } else {
                            // Fallback modal close
                            const modal = document.getElementById('courseContentModal');
                            if (modal) {
                                modal.style.display = 'none';
                            }
                        }

                        if (typeof window.navigateToLesson === 'function') {
                            window.navigateToLesson(lessonId);
                        } else {
                            // Fallback navigation
                            console.warn('⚠️ navigateToLesson not available, using fallback');
                            window.location.href = `?course=${ASG_CONFIG.COURSE_CODE}&lesson=${lessonId}`;
                        }
                    };

                    // DEBUG FUNCTION - Remove after testing
                    // window.debugLessonState = debugLessonState; // Commented out - function not defined

                    // ===== INLINE EDITING FUNCTIONALITY =====

                    /**
                     * Initialize inline editing for admin users
                     */
                    function initializeInlineEditing() {
                        console.log('🔧 Initializing inline editing for admin user');

                        // Wait for lesson content to be loaded
                        const observer = new MutationObserver((mutations) => {
                            mutations.forEach((mutation) => {
                                if (mutation.type === 'childList' && mutation.target.id === 'lessonContentArea') {
                                    // Content has been updated, add edit indicators
                                    setTimeout(() => addEditIndicators(), 100);
                                }
                            });
                        });

                        const lessonContentArea = document.getElementById('lessonContentArea');
                        if (lessonContentArea) {
                            observer.observe(lessonContentArea, { childList: true, subtree: true });
                        }

                        // Add edit indicators to current content if already loaded
                        setTimeout(() => addEditIndicators(), 500);
                    }

                    /**
                     * Add edit indicators to editable elements
                     */
                    function addEditIndicators() {
                        if (!ASG_CONFIG.IS_ADMIN || !asgState.currentLesson) return;

                        console.log('🔧 Adding edit indicators to lesson content');

                        // Add edit indicator to lesson title - check all possible title selectors
                        const titleSelectors = [
                            '.lesson-title-main',
                            '.lesson-title-basic',
                            '.lesson-title-featured',
                            '.lesson-title-two-columns',
                            '.lesson-title-medium',
                            '.lesson-title-hero',
                            'h1',
                            '.lesson-header h1',
                            '.lesson-content h1'
                        ];

                        let titleElement = null;
                        for (const selector of titleSelectors) {
                            titleElement = document.querySelector(selector);
                            if (titleElement) {
                                console.log('📝 Found lesson title with selector:', selector);
                                break;
                            }
                        }

                        if (titleElement && !titleElement.classList.contains('admin-edit-indicator')) {
                            titleElement.classList.add('admin-edit-indicator');
                            titleElement.setAttribute('data-edit-type', 'title');
                            titleElement.addEventListener('click', handleEditClick);
                            console.log('✅ Added edit indicator to lesson title');
                        } else if (!titleElement) {
                            console.log('❌ No lesson title element found with any selector');
                        }

                        // Add edit indicators to content sections based on template
                        const template = asgState.currentLesson.lesson_template || 'basic';

                        if (template === 'triple_layout') {
                            // Triple layout has hero, left, and right content
                            console.log('🔧 Adding edit indicators for triple_layout template');

                            // Hero content (texto principal del hero)
                            addEditIndicatorToElement('.hero-content', 'hero_content');

                            // Left content (texto de la sección izquierda)
                            addEditIndicatorToElement('.triple-left-section .section-content-right', 'left_content');

                            // Right content (texto de la sección derecha)
                            addEditIndicatorToElement('.triple-right-section .section-content-left', 'right_content');

                        } else if (template === 'two_columns') {
                            // Two columns template - imagen izq, texto der
                            console.log('🔧 Adding edit indicators for two_columns template');
                            addEditIndicatorToElement('.column-content', 'content');

                        } else if (template === 'hero_image') {
                            // Hero image template - imagen grande arriba, texto abajo
                            console.log('🔧 Adding edit indicators for hero_image template');
                            addEditIndicatorToElement('.hero-text-content', 'content');

                        } else if (template === 'featured_image') {
                            // Featured image template - imagen destacada + contenido
                            console.log('🔧 Adding edit indicators for featured_image template');
                            addEditIndicatorToElement('.featured-text-content', 'content');

                        } else if (template === 'medium_right') {
                            // Medium right template - imagen izq mediana, texto der
                            console.log('🔧 Adding edit indicators for medium_right template');
                            addEditIndicatorToElement('.medium-content-right', 'content');

                        } else if (template === 'medium_left') {
                            // Medium left template - texto izq, imagen der mediana
                            console.log('🔧 Adding edit indicators for medium_left template');
                            addEditIndicatorToElement('.medium-content-left', 'content');

                        } else if (template === 'basic' || !template) {
                            // Basic content editing
                            console.log('🔧 Adding edit indicators for basic template');
                            addEditIndicatorToElement('.basic-text-content', 'content');

                        } else {
                            // Other templates - try to find any content area
                            console.log('🔧 Adding edit indicators for unknown template:', template);
                            addEditIndicatorToElement('.basic-text-content, .featured-text-content, .hero-text-content, .medium-content-right, .medium-content-left, .column-content', 'content');
                        }

                        // Add edit indicators to images
                        addImageEditIndicators();
                    }

                    /**
                     * Add edit indicator to a specific element
                     */
                    function addEditIndicatorToElement(selector, editType) {
                        console.log(`🔍 Looking for element: "${selector}" for editType: "${editType}"`);

                        const element = document.querySelector(selector);
                        if (element) {
                            if (!element.classList.contains('admin-edit-indicator')) {
                                element.classList.add('admin-edit-indicator');
                                element.setAttribute('data-edit-type', editType);
                                element.addEventListener('click', handleEditClick);
                                console.log(`✅ Added edit indicator to: "${selector}" (${editType})`);
                                console.log('🔍 Element content preview:', element.textContent.substring(0, 50) + '...');
                            } else {
                                console.log(`⚠️ Element already has edit indicator: "${selector}"`);
                            }
                        } else {
                            console.warn(`❌ Element not found: "${selector}" for editType: "${editType}"`);
                            // Try to find similar elements for debugging
                            const allElements = document.querySelectorAll('div, p, section, article');
                            console.log('🔍 Available elements with content:',
                                Array.from(allElements)
                                    .filter(el => el.textContent.trim().length > 10)
                                    .slice(0, 5)
                                    .map(el => ({
                                        tag: el.tagName,
                                        classes: el.className,
                                        content: el.textContent.substring(0, 30) + '...'
                                    }))
                            );
                        }
                    }

                    /**
                     * Add edit indicators to images
                     */
                    function addImageEditIndicators() {
                        const images = document.querySelectorAll('img:not(.admin-edit-indicator)');
                        images.forEach(img => {
                            // Add edit indicator directly to the image
                            img.classList.add('admin-edit-indicator');
                            img.setAttribute('data-edit-type', 'image');
                            img.style.cursor = 'pointer';
                            img.addEventListener('click', handleImageEditClick);

                            // Add visual indicator
                            img.style.position = 'relative';
                            img.title = 'Click to change image (Admin)';
                        });
                    }

                    /**
                     * Handle edit click for text content
                     */
                    function handleEditClick(event) {
                        event.preventDefault();
                        event.stopPropagation();

                        const element = event.currentTarget;
                        const editType = element.getAttribute('data-edit-type');

                        console.log('🔧 Edit clicked:', editType);

                        if (element.querySelector('.inline-editor')) {
                            return; // Already editing
                        }

                        createInlineEditor(element, editType);
                    }

                    /**
                     * Handle image edit click
                     */
                    function handleImageEditClick(event) {
                        event.preventDefault();
                        event.stopPropagation();

                        const img = event.currentTarget;
                        console.log('🔧 Image edit clicked:', img);

                        // Create file input for image upload
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';
                        input.onchange = (e) => handleImageUpload(e, img);
                        input.click();
                    }

                    /**
                     * Create inline editor for text content
                     */
                    function createInlineEditor(element, editType) {
                        const originalContent = element.innerHTML;
                        const isTitle = editType === 'title';

                        // Create editor container
                        const editor = document.createElement('div');
                        editor.className = 'inline-editor';

                        // Create input/textarea
                        const input = document.createElement(isTitle ? 'input' : 'textarea');
                        input.value = element.textContent.trim();

                        if (!isTitle) {
                            input.rows = Math.max(3, Math.ceil(input.value.length / 50));
                        }

                        // Create action buttons
                        const actions = document.createElement('div');
                        actions.className = 'inline-editor-actions';

                        const saveBtn = document.createElement('button');
                        saveBtn.className = 'inline-editor-btn save';
                        saveBtn.textContent = 'Save';
                        saveBtn.type = 'button';

                        saveBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            saveInlineEdit(element, input.value, editType, editor, originalContent);
                        });

                        const cancelBtn = document.createElement('button');
                        cancelBtn.className = 'inline-editor-btn cancel';
                        cancelBtn.textContent = 'Cancel';
                        cancelBtn.type = 'button';
                        console.log('🔧 Creating Cancel button');

                        cancelBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('🖱️ Cancel button clicked!');
                            cancelInlineEdit(element, editor, originalContent);
                        });

                        actions.appendChild(saveBtn);
                        actions.appendChild(cancelBtn);

                        editor.appendChild(input);
                        editor.appendChild(actions);

                        // Replace element content with editor
                        element.innerHTML = '';
                        element.appendChild(editor);

                        // Focus input
                        input.focus();
                        if (isTitle) {
                            input.select();
                        }

                        // Handle Enter key
                        input.addEventListener('keydown', (e) => {
                            if (e.key === 'Enter' && (isTitle || e.ctrlKey)) {
                                e.preventDefault();
                                saveInlineEdit(element, input.value, editType, editor, originalContent);
                            } else if (e.key === 'Escape') {
                                e.preventDefault();
                                cancelInlineEdit(element, editor, originalContent);
                            }
                        });
                    }

                    /**
                     * Save inline edit
                     */
                    async function saveInlineEdit(element, newValue, editType, editor, originalContent) {
                        if (!newValue.trim()) {
                            alert('Content cannot be empty');
                            return;
                        }

                        // Show loading
                        const loading = document.createElement('div');
                        loading.className = 'inline-editor-loading';
                        loading.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
                        editor.appendChild(loading);

                        try {
                            const success = await updateLessonContent(editType, newValue);

                            if (success) {
                                // Update element content
                                element.innerHTML = editType === 'title' ? newValue : `<p>${newValue}</p>`;
                                element.classList.add('admin-edit-success');

                                // Re-add edit indicator
                                setTimeout(() => {
                                    element.classList.remove('admin-edit-success');
                                    element.classList.add('admin-edit-indicator');
                                    element.setAttribute('data-edit-type', editType);
                                    element.addEventListener('click', handleEditClick);
                                }, 2000);

                                console.log('✅ Content updated successfully');
                            } else {
                                throw new Error('Failed to save changes');
                            }
                        } catch (error) {
                            console.error('❌ Error saving content:', error);
                            alert('Error saving changes: ' + error.message);
                            cancelInlineEdit(element, editor, originalContent);
                        }
                    }

                    /**
                     * Cancel inline edit
                     */
                    function cancelInlineEdit(element, editor, originalContent) {
                        console.log('🚫 CANCEL CLICKED - Starting cancel process');
                        console.log('Element:', element);
                        console.log('Editor:', editor);
                        console.log('Original content:', originalContent);

                        try {
                            // Remove the editor from DOM
                            if (editor && editor.parentNode) {
                                editor.parentNode.removeChild(editor);
                                console.log('✅ Editor removed from DOM');
                            }

                            // Restore original content
                            element.innerHTML = originalContent;
                            console.log('✅ Original content restored');

                            // Re-add the edit indicator and event listener
                            element.classList.add('admin-edit-indicator');
                            element.addEventListener('click', handleEditClick);
                            console.log('✅ Edit indicator and listener re-added');

                        } catch (error) {
                            console.error('❌ Error in cancelInlineEdit:', error);
                        }

                        console.log('✅ CANCEL COMPLETED');
                    }

                    /**
                     * Update lesson content via API
                     */
                    async function updateLessonContent(editType, newValue) {
                        if (!asgState.currentLesson) {
                            throw new Error('No current lesson available');
                        }

                        const lesson = asgState.currentLesson;
                        const courseCode = ASG_CONFIG.COURSE_CODE;

                        // Debug: Log lesson data to identify correct field names
                        console.log('🔍 Current lesson data:', lesson);
                        console.log('🔍 Available lesson fields:', Object.keys(lesson));

                        // Try multiple possible field names for module_id
                        const moduleId = lesson.module_id || lesson.id_module || lesson.moduleId || lesson.id_modules;

                        // Try multiple possible field names for lesson_id
                        const lessonId = lesson.id_lesson || lesson.lesson_id || lesson.id || lesson.lessonId;

                        console.log('🔍 Extracted IDs:', { courseCode, moduleId, lessonId });
                        console.log('🔍 Module ID attempts:', {
                            'lesson.module_id': lesson.module_id,
                            'lesson.id_module': lesson.id_module,
                            'lesson.moduleId': lesson.moduleId,
                            'lesson.id_modules': lesson.id_modules
                        });

                        if (!moduleId || !lessonId) {
                            console.error('❌ Missing required IDs:', { moduleId, lessonId });
                            console.error('❌ Full lesson object:', lesson);
                            throw new Error(`Missing required IDs: moduleId=${moduleId}, lessonId=${lessonId}`);
                        }

                        // Prepare update data based on edit type
                        let updateData = {};

                        if (editType === 'title') {
                            updateData.title_lesson = newValue;
                        } else if (editType === 'content') {
                            updateData.content_lesson = `<p>${newValue}</p>`;
                        } else if (editType === 'lesson_images') {
                            // Handle lesson images update
                            updateData.lesson_images = newValue;
                        } else if (editType.includes('_content')) {
                            // Handle lesson sections (hero_content, left_content, right_content)
                            const currentSections = lesson.lesson_sections ?
                                JSON.parse(lesson.lesson_sections) : {};
                            currentSections[editType] = newValue;
                            updateData.lesson_sections = JSON.stringify(currentSections);
                        }

                        console.log('📤 Updating lesson:', { courseCode, moduleId, lessonId, updateData });

                        // Make API request
                        const response = await fetch(`${ASG_CONFIG.API_BASE}/courses/${courseCode}/modules/${moduleId}/lessons/${lessonId}/api`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                            },
                            body: JSON.stringify(updateData)
                        });

                        const result = await response.json();

                        if (result.success) {
                            // Update local lesson data
                            Object.assign(lesson, updateData);
                            if (updateData.lesson_sections) {
                                lesson.lesson_sections = updateData.lesson_sections;
                            }
                            return true;
                        } else {
                            throw new Error(result.error || 'API request failed');
                        }
                    }

                    /**
                     * Handle image upload
                     */
                    async function handleImageUpload(event, img) {
                        const file = event.target.files[0];
                        if (!file) return;

                        console.log('🖼️ Uploading image:', file.name);
                        console.log('🔍 Target image element:', img);

                        try {
                            // Validate that we have an image element
                            if (!img || img.tagName !== 'IMG') {
                                console.error('❌ Invalid image element:', img);
                                throw new Error('Invalid image element');
                            }

                            // Show loading indicator
                            const originalSrc = img.src;
                            img.style.opacity = '0.5';

                            // Create FormData for upload (compatible with edit_course.php)
                            const formData = new FormData();
                            formData.append('image', file);
                            formData.append('type', 'lesson_image');
                            formData.append('course_id', asgState.currentCourse?.id_course || '');

                            console.log('📤 Uploading to:', `${ASG_CONFIG.API_BASE}/media/api`);
                            console.log('📤 FormData contents:', {
                                image: file.name,
                                type: 'lesson_image',
                                course_id: asgState.currentCourse?.id_course || 'undefined'
                            });

                            // Upload image using same method as edit_course.php
                            const response = await fetch(`${ASG_CONFIG.API_BASE}/media/api`, {
                                method: 'POST',
                                headers: {
                                    'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                                },
                                body: formData
                            });

                            const result = await response.json();

                            if (result.success && result.data) {
                                // Update image source
                                img.src = result.data.url;
                                img.style.opacity = '1';

                                console.log('✅ Image uploaded successfully:', result.data.url);

                                // Now update the lesson in the database
                                await updateLessonImage(img, result.data.url);

                                // Show success animation
                                img.classList.add('admin-edit-success');
                                setTimeout(() => {
                                    img.classList.remove('admin-edit-success');
                                }, 2000);
                            } else {
                                throw new Error(result.error || 'Image upload failed');
                            }
                        } catch (error) {
                            console.error('❌ Error uploading image:', error);
                            alert('Error uploading image: ' + error.message);
                            img.style.opacity = '1';
                        }
                    }

                    /**
                     * Update lesson image in database
                     */
                    async function updateLessonImage(imgElement, newImageUrl) {
                        if (!asgState.currentLesson) {
                            throw new Error('No current lesson available');
                        }

                        console.log('🔄 Updating lesson image in database...');
                        console.log('🔍 Image element:', imgElement);
                        console.log('🔍 New URL:', newImageUrl);

                        // Identify image type based on element attributes or position
                        const imageType = identifyImageType(imgElement);
                        console.log('🔍 Identified image type:', imageType);

                        if (!imageType) {
                            console.warn('⚠️ Could not identify image type, skipping database update');
                            return;
                        }

                        // Update lesson_images JSON
                        const lesson = asgState.currentLesson;
                        let lessonImages = {};

                        try {
                            lessonImages = lesson.lesson_images ? JSON.parse(lesson.lesson_images) : {};
                        } catch (e) {
                            console.warn('⚠️ Error parsing lesson_images, creating new object');
                            lessonImages = {};
                        }

                        // Update the specific image type
                        lessonImages[imageType] = {
                            url: newImageUrl,
                            id: null, // Will be set by backend if needed
                            alt: imgElement.alt || '',
                            description: ''
                        };

                        // Prepare update data
                        const updateData = {
                            lesson_images: JSON.stringify(lessonImages)
                        };

                        console.log('📤 Updating lesson with new image data:', updateData);

                        // Use existing updateLessonContent function
                        const success = await updateLessonContent('lesson_images', JSON.stringify(lessonImages));

                        if (success) {
                            console.log('✅ Lesson image updated in database successfully');
                            // Update local lesson data
                            lesson.lesson_images = JSON.stringify(lessonImages);
                        } else {
                            throw new Error('Failed to update lesson image in database');
                        }
                    }

                    /**
                     * Identify image type based on element context
                     */
                    function identifyImageType(imgElement) {
                        // Method 1: Check parent element classes
                        const parent = imgElement.closest('.hero-section, .triple-hero-content, .hero-content');
                        if (parent) {
                            return 'tripleHero';
                        }

                        const leftParent = imgElement.closest('.left-section, .triple-left-content, .left-content');
                        if (leftParent) {
                            return 'tripleLeft';
                        }

                        const rightParent = imgElement.closest('.right-section, .triple-right-content, .right-content');
                        if (rightParent) {
                            return 'tripleRight';
                        }

                        // Method 2: Check image src against current lesson_images
                        if (asgState.currentLesson && asgState.currentLesson.lesson_images) {
                            try {
                                const lessonImages = JSON.parse(asgState.currentLesson.lesson_images);
                                const currentSrc = imgElement.src;

                                for (const [type, imageData] of Object.entries(lessonImages)) {
                                    if (imageData.url === currentSrc) {
                                        return type;
                                    }
                                }
                            } catch (e) {
                                console.warn('⚠️ Error parsing lesson_images for type identification');
                            }
                        }

                        // Method 3: Check data attributes
                        const dataType = imgElement.getAttribute('data-image-type');
                        if (dataType) {
                            return dataType;
                        }

                        // Fallback: assume it's hero image
                        console.warn('⚠️ Could not identify image type, defaulting to tripleHero');
                        return 'tripleHero';
                    }

                    /**
                     * ===== ADD LESSON FUNCTIONALITY =====
                     */

                    /**
                     * Show add lesson modal
                     */
                    function showAddLessonModal() {
                        if (!ASG_CONFIG.IS_ADMIN) {
                            console.warn('⚠️ Add lesson feature only available for admins');
                            return;
                        }

                        console.log('📝 Opening add lesson modal');

                        // Close the dropdown menu first
                        const menu = document.getElementById('addMenu');
                        const btn = document.getElementById('mainAddBtn');
                        if (menu) menu.classList.remove('show');
                        if (btn) btn.style.transform = '';

                        const modal = document.getElementById('addLessonModal');
                        if (modal) {
                            // Clear previous data
                            window.lessonImages = {};

                            // Reset form
                            const form = document.getElementById('addLessonForm');
                            if (form) {
                                form.reset();
                            }

                            // Populate module selector
                            populateModuleSelector();

                            modal.style.display = 'flex';

                            // Initialize with basic template
                            selectTemplate('basic');

                            // Focus on title input
                            const titleInput = document.getElementById('lessonTitle');
                            if (titleInput) {
                                setTimeout(() => titleInput.focus(), 100);
                            }
                        }
                    }

                    /**
                     * Close add lesson modal
                     */
                    function closeAddLessonModal() {
                        console.log('❌ Closing add lesson modal');
                        const modal = document.getElementById('addLessonModal');
                        if (modal) {
                            modal.style.display = 'none';

                            // Reset form
                            const form = document.getElementById('addLessonForm');
                            if (form) {
                                form.reset();
                            }

                            // Clear uploaded images
                            window.lessonImages = {};
                        }
                    }

                    /**
                     * Handle add lesson form submission
                     */
                    async function handleAddLessonSubmit(event) {
                        event.preventDefault();

                        const form = event.target;
                        const formData = new FormData(form);
                        const template = document.getElementById('selectedTemplate').value;

                        // Collect all form data
                        const lessonData = {
                            title: formData.get('title'),
                            type: formData.get('type'),
                            template: template,
                            module_id: formData.get('module_id'),
                            is_preview: formData.get('is_preview') ? '1' : '0'
                        };

                        // Generate placeholder content based on template
                        lessonData.placeholderData = generatePlaceholderData(template);

                        // No images for initial creation - will be added via inline editing
                        lessonData.lesson_images = {};

                        console.log('📝 Creating new lesson:', lessonData);

                        try {
                            await createNewLesson(lessonData);
                        } catch (error) {
                            console.error('❌ Error in form submission:', error);
                        }
                    }

                    // Global object to store uploaded images (same as edit_course.php)
                    if (!window.lessonImages) {
                        window.lessonImages = {};
                    }

                    /**
                     * Generate placeholder data based on template
                     */
                    function generatePlaceholderData(template) {
                        const placeholders = {
                            basic: {
                                content: '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p><p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>'
                            },
                            featured_image: {
                                content: '<p>This lesson features a prominent image at the top. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p><p>Click on the image area above to upload your featured image, then click here to edit this content.</p>'
                            },
                            two_columns: {
                                content: '<p>This is a two-column layout with an image on the left and content on the right.</p><p>Click on the image area to upload your image, then click here to edit this content.</p>'
                            },
                            hero_image: {
                                content: '<p>This lesson starts with a large hero image with the title overlaid.</p><p>Click on the hero image area to upload your image, then click here to edit this content.</p>'
                            },
                            medium_right: {
                                content: '<p>This layout has a medium-sized image on the left with content flowing to the right.</p><p>Click on the image area to upload your image, then click here to edit this content.</p>'
                            },
                            medium_left: {
                                content: '<p>This layout has content on the left with a medium-sized image on the right.</p><p>Click on the image area to upload your image, then click here to edit this content.</p>'
                            },
                            triple_layout: {
                                lesson_sections: {
                                    main_content: '',
                                    hero_content: 'This is the hero section content. Click here to edit this text and make it your own.',
                                    left_content: 'This is the left section content. It appears to the right of the left image. Click here to edit.',
                                    right_content: 'This is the right section content. It appears to the left of the right image. Click here to edit.'
                                }
                            }
                        };

                        return placeholders[template] || placeholders.basic;
                    }

                    /**
                     * Create new lesson via API
                     */
                    async function createNewLesson(lessonData) {
                        console.log('🚀 Starting createNewLesson with data:', lessonData);
                        console.log('🔍 Current state:', {
                            currentCourse: asgState.currentCourse ? 'Available' : 'Missing',
                            currentLesson: asgState.currentLesson ? 'Available' : 'Missing',
                            courseCode: ASG_CONFIG.COURSE_CODE
                        });

                        if (!asgState.currentCourse) {
                            console.error('❌ No current course available');
                            throw new Error('No current course available');
                        }

                        // Get module ID from form data
                        const moduleId = lessonData.module_id;
                        console.log('🔍 Module ID from form:', moduleId);

                        if (!moduleId) {
                            console.error('❌ No module ID selected');
                            throw new Error('Please select a module for this lesson');
                        }

                        const courseCode = ASG_CONFIG.COURSE_CODE;

                        console.log('📤 Creating lesson in course:', courseCode, 'module:', moduleId);

                        // Get submit button and store original text
                        const submitBtn = document.querySelector('#addLessonModal button[type="submit"]');
                        let originalText = '<i class="bi bi-plus-circle me-1"></i>Create Lesson';

                        try {
                            // Show loading state
                            if (submitBtn) {
                                originalText = submitBtn.innerHTML;
                                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Creating...';
                                submitBtn.disabled = true;
                            } else {
                                console.warn('⚠️ Submit button not found for loading state');
                            }

                            // Prepare API payload
                            const apiPayload = {
                                title_lesson: lessonData.title,
                                lesson_type: lessonData.type,
                                lesson_template: lessonData.template,
                                order_lesson: getNextLessonOrder(),
                                is_preview: lessonData.is_preview
                            };

                            // Add placeholder content based on template
                            const placeholderData = lessonData.placeholderData;
                            const template = lessonData.template;

                            if (template === 'triple_layout' && placeholderData.lesson_sections) {
                                apiPayload.lesson_sections = JSON.stringify(placeholderData.lesson_sections);
                                apiPayload.content_lesson = '<p></p>'; // Empty for triple layout
                                console.log('📝 lesson_sections JSON:', apiPayload.lesson_sections);
                            } else {
                                apiPayload.content_lesson = placeholderData.content || '<p>Default lesson content</p>';
                            }

                            // No images initially - will be added via inline editing
                            apiPayload.lesson_images = null;
                            console.log('🖼️ No initial images - will be added via inline editing');

                            console.log('📤 API Payload:', apiPayload);

                            const response = await fetch(`${ASG_CONFIG.API_BASE}/courses/${courseCode}/modules/${moduleId}/lessons/api`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                                },
                                body: JSON.stringify(apiPayload)
                            });

                            const result = await response.json();

                            if (result.success && result.data) {
                                console.log('✅ Lesson created successfully:', result.data);

                                // Close modal
                                closeAddLessonModal();

                                // Show success notification
                                showNotification('¡Lección creada exitosamente!', 'success');

                                // Reload course data to include new lesson
                                await loadCourseData();

                                // Navigate to new lesson
                                const newLessonId = result.data.id_lesson || result.data.id;
                                if (newLessonId) {
                                    setTimeout(() => {
                                        window.location.href = `?course=${courseCode}&lesson=${newLessonId}`;
                                    }, 1000);
                                }

                            } else {
                                throw new Error(result.error || 'Error al crear la lección');
                            }

                        } catch (error) {
                            console.error('❌ Error creating lesson:', error);
                            showNotification('Error al crear la lección: ' + error.message, 'error');

                        } finally {
                            // Restore button state
                            if (submitBtn) {
                                submitBtn.innerHTML = originalText;
                                submitBtn.disabled = false;
                            }
                        }
                    }

                    /**
                     * Populate module selector in add lesson modal
                     */
                    function populateModuleSelector() {
                        const selector = document.getElementById('lessonModule');
                        if (!selector) return;

                        // Clear existing options
                        selector.innerHTML = '';

                        if (!asgState.currentCourse || !asgState.currentCourse.modules) {
                            selector.innerHTML = '<option value="">No modules available</option>';
                            return;
                        }

                        const modules = asgState.currentCourse.modules;
                        const currentModuleId = getCurrentModuleId();

                        // Add modules as options
                        modules.forEach(module => {
                            const option = document.createElement('option');
                            option.value = module.id_modules || module.id;
                            option.textContent = `${module.order_module || '?'}. ${module.title_module || module.title || 'Untitled Module'}`;

                            // Select current module by default
                            if (currentModuleId && (module.id_modules == currentModuleId || module.id == currentModuleId)) {
                                option.selected = true;
                            }

                            selector.appendChild(option);
                        });

                        // If no current module detected, select first one
                        if (!currentModuleId && modules.length > 0) {
                            selector.selectedIndex = 0;
                        }

                        console.log('📋 Module selector populated with', modules.length, 'modules');
                    }

                    /**
                     * Get current module ID
                     */
                    function getCurrentModuleId() {
                        // Try to get from current lesson
                        if (asgState.currentLesson && asgState.currentLesson.module_id) {
                            return asgState.currentLesson.module_id;
                        }

                        // Try to get from URL parameters
                        const urlParams = new URLSearchParams(window.location.search);
                        const moduleParam = urlParams.get('module');
                        if (moduleParam) {
                            return moduleParam;
                        }

                        // Try to get from first module in course
                        if (asgState.currentCourse && asgState.currentCourse.modules && asgState.currentCourse.modules.length > 0) {
                            return asgState.currentCourse.modules[0].id_modules || asgState.currentCourse.modules[0].id;
                        }

                        console.warn('⚠️ Could not determine current module ID');
                        return null;
                    }

                    /**
                     * Get next lesson order number
                     */
                    function getNextLessonOrder() {
                        if (!asgState.lessonsList || asgState.lessonsList.length === 0) {
                            return 1;
                        }

                        // Find highest order number and add 1
                        const maxOrder = Math.max(...asgState.lessonsList.map(lesson =>
                            parseInt(lesson.order_lesson || lesson.order || 0)
                        ));

                        return maxOrder + 1;
                    }

                    /**
                     * Handle lesson type change
                     */
                    function handleLessonTypeChange() {
                        const lessonType = document.getElementById('lessonType').value;
                        const templateGroup = document.getElementById('templateSelectorGroup');

                        if (lessonType === 'text') {
                            templateGroup.style.display = 'block';
                        } else {
                            templateGroup.style.display = 'none';
                            selectTemplate('basic'); // Default for non-text lessons
                        }
                    }

                    /**
                     * Select template and update UI
                     */
                    function selectTemplate(template) {
                        console.log('🎨 Template selected:', template);

                        // Update visual selection
                        document.querySelectorAll('.template-option').forEach(option => {
                            option.classList.remove('selected');
                        });

                        const selectedOption = document.querySelector(`[data-template="${template}"]`);
                        if (selectedOption) {
                            selectedOption.classList.add('selected');
                        }

                        // Update hidden input
                        document.getElementById('selectedTemplate').value = template;

                        // Show template preview
                        showTemplatePreview(template);
                    }

                    /**
                     * Show template preview with placeholders
                     */
                    function showTemplatePreview(template) {
                        const container = document.getElementById('templatePreview');
                        if (!container) return;

                        let previewHtml = '';

                        switch (template) {
                            case 'basic':
                                previewHtml = generateBasicPreview();
                                break;
                            case 'featured_image':
                                previewHtml = generateFeaturedImagePreview();
                                break;
                            case 'two_columns':
                                previewHtml = generateTwoColumnsPreview();
                                break;
                            case 'hero_image':
                                previewHtml = generateHeroImagePreview();
                                break;
                            case 'medium_right':
                                previewHtml = generateMediumRightPreview();
                                break;
                            case 'medium_left':
                                previewHtml = generateMediumLeftPreview();
                                break;
                            case 'triple_layout':
                                previewHtml = generateTripleLayoutPreview();
                                break;
                            default:
                                previewHtml = generateBasicPreview();
                        }

                        container.innerHTML = previewHtml;
                    }

                    /**
                     * Handle image upload for lesson creation (same as edit_course.php)
                     */
                    window.handleImageUpload = function(imageType, input) {
                        console.log('🔧 handleImageUpload called:', imageType, input);

                        if (!input || !input.files || input.files.length === 0) {
                            console.warn('⚠️ No file selected or input invalid');
                            return;
                        }

                        const file = input.files[0];
                        if (!file) return;

                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                            showToast('Please select a valid image file', 'error');
                            return;
                        }

                        // Validate file size (max 5MB)
                        if (file.size > 5 * 1024 * 1024) {
                            showToast('Image size must be less than 5MB', 'error');
                            return;
                        }

                        // Update file name display
                        const nameSpan = document.getElementById(imageType + 'ImageName');
                        if (nameSpan) {
                            nameSpan.textContent = file.name;
                        }

                        // Show preview
                        const preview = document.getElementById(imageType + 'ImagePreview');
                        const previewImg = preview?.querySelector('img');

                        if (preview && previewImg) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                previewImg.src = e.target.result;
                                preview.classList.remove('hidden');
                            };
                            reader.readAsDataURL(file);
                        }

                        // Upload image
                        uploadImageToServer(file, imageType);
                    }

                    /**
                     * Upload image to server (same as edit_course.php)
                     */
                    async function uploadImageToServer(file, imageType) {
                        try {
                            showToast('Uploading image...', 'info');

                            const formData = new FormData();
                            formData.append('image', file);
                            formData.append('type', 'lesson_image');
                            formData.append('image_type', imageType);

                            const response = await fetch(`${ASG_CONFIG.API_BASE}/media/api`, {
                                method: 'POST',
                                body: formData,
                                headers: {
                                    'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                                }
                            });

                            const result = await response.json();

                            if (result.success && result.data) {
                                // Store uploaded image data (same as edit_course.php)
                                window.lessonImages[imageType] = {
                                    url: result.data.url,
                                    id: result.data.id,
                                    alt: '',
                                    description: ''
                                };

                                showToast('Image uploaded successfully', 'success');
                                console.log('📷 Image uploaded:', imageType, result.data);
                            } else {
                                throw new Error(result.message || 'Upload failed');
                            }
                        } catch (error) {
                            console.error('❌ Image upload error:', error);
                            showToast('Failed to upload image: ' + error.message, 'error');
                        }
                    }

                    /**
                     * Simple toast notification function
                     */
                    function showToast(message, type = 'info') {
                        console.log(`🍞 Toast (${type}):`, message);

                        // Create toast element
                        const toast = document.createElement('div');
                        toast.className = `toast toast-${type}`;
                        toast.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff'};
                            color: white;
                            padding: 12px 20px;
                            border-radius: 6px;
                            z-index: 9999;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                            max-width: 300px;
                            font-size: 14px;
                        `;
                        toast.textContent = message;

                        document.body.appendChild(toast);

                        // Remove after 3 seconds
                        setTimeout(() => {
                            if (toast.parentNode) {
                                toast.parentNode.removeChild(toast);
                            }
                        }, 3000);
                    }

                    /**
                     * Show add module modal
                     */
                    function showAddModuleModal() {
                        if (!ASG_CONFIG.IS_ADMIN) {
                            console.warn('⚠️ Add module feature only available for admins');
                            return;
                        }

                        console.log('📚 Opening add module modal');
                        const modal = document.getElementById('addModuleModal');
                        if (modal) {
                            modal.style.display = 'flex';

                            // Reset form
                            const form = document.getElementById('addModuleForm');
                            if (form) {
                                form.reset();
                            }

                            // Focus on title input
                            const titleInput = document.getElementById('moduleTitle');
                            if (titleInput) {
                                setTimeout(() => titleInput.focus(), 100);
                            }
                        }
                    }

                    /**
                     * Close add module modal
                     */
                    function closeAddModuleModal() {
                        console.log('❌ Closing add module modal');
                        const modal = document.getElementById('addModuleModal');
                        if (modal) {
                            modal.style.display = 'none';

                            // Reset form
                            const form = document.getElementById('addModuleForm');
                            if (form) {
                                form.reset();
                            }
                        }
                    }

                    /**
                     * Handle add module form submission
                     */
                    async function handleAddModuleSubmit(event) {
                        event.preventDefault();

                        const form = event.target;
                        const formData = new FormData(form);

                        const moduleData = {
                            title: formData.get('title'),
                            description: formData.get('description') || ''
                        };

                        console.log('📚 Creating new module:', moduleData);

                        try {
                            await createNewModule(moduleData);
                        } catch (error) {
                            console.error('❌ Error in module form submission:', error);
                        }
                    }

                    /**
                     * Create new module via API
                     */
                    async function createNewModule(moduleData) {
                        console.log('🚀 Starting createNewModule with data:', moduleData);

                        if (!asgState.currentCourse) {
                            console.error('❌ No current course available');
                            throw new Error('No current course available');
                        }

                        const courseCode = ASG_CONFIG.COURSE_CODE;
                        console.log('📤 Creating module in course:', courseCode);

                        // Get submit button and store original text
                        const submitBtn = document.querySelector('#addModuleModal button[type="submit"]');
                        let originalText = '<i class="bi bi-plus-circle me-1"></i>Create Module';

                        try {
                            // Show loading state
                            if (submitBtn) {
                                originalText = submitBtn.innerHTML;
                                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Creating...';
                                submitBtn.disabled = true;
                            }

                            // Prepare API payload
                            const apiPayload = {
                                title_module: moduleData.title,
                                description_module: moduleData.description,
                                order_module: getNextModuleOrder(),
                                duration_module: 0
                            };

                            console.log('📤 API Payload:', apiPayload);

                            const response = await fetch(`${ASG_CONFIG.API_BASE}/courses/${courseCode}/modules/api`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                                },
                                body: JSON.stringify(apiPayload)
                            });

                            const result = await response.json();
                            console.log('📥 API Response:', result);

                            if (result.success) {
                                showToast('Module created successfully!', 'success');
                                closeAddModuleModal();

                                // Reload course data to show new module
                                await loadCourseData();

                                console.log('✅ Module created successfully');
                            } else {
                                throw new Error(result.error || result.message || 'Failed to create module');
                            }

                        } catch (error) {
                            console.error('❌ Error creating module:', error);
                            showToast('Error creating module: ' + error.message, 'error');
                        } finally {
                            // Restore button state
                            if (submitBtn) {
                                submitBtn.innerHTML = originalText;
                                submitBtn.disabled = false;
                            }
                        }
                    }

                    /**
                     * Get next module order
                     */
                    function getNextModuleOrder() {
                        if (!asgState.currentCourse || !asgState.currentCourse.modules) {
                            return 1;
                        }

                        const maxOrder = Math.max(...asgState.currentCourse.modules.map(m => parseInt(m.order_module) || 0));
                        return maxOrder + 1;
                    }

                    /**
                     * Toggle add menu dropdown
                     */
                    function toggleAddMenu() {
                        const menu = document.getElementById('addMenu');
                        const btn = document.getElementById('mainAddBtn');

                        if (menu.classList.contains('show')) {
                            menu.classList.remove('show');
                            btn.style.transform = '';
                        } else {
                            menu.classList.add('show');
                            btn.style.transform = 'translateY(-2px) rotate(45deg)';
                        }
                    }
                    /**
                     * Close dropdown when clicking outside
                     */
                    document.addEventListener('click', function(event) {
                        const container = document.querySelector('.floating-add-container');
                        const menu = document.getElementById('addMenu');
                        const btn = document.getElementById('mainAddBtn');

                        if (container && !container.contains(event.target)) {
                            if (menu && menu.classList.contains('show')) {
                                menu.classList.remove('show');
                                if (btn) btn.style.transform = '';
                            }
                        }
                    });

                    // Make functions globally available
                    window.showAddLessonModal = showAddLessonModal;
                    window.closeAddLessonModal = closeAddLessonModal;
                    window.handleLessonTypeChange = handleLessonTypeChange;
                    window.selectTemplate = selectTemplate;
                    window.showToast = showToast;
                    window.toggleAddMenu = toggleAddMenu;
                    window.showAddModuleModal = showAddModuleModal;
                    window.closeAddModuleModal = closeAddModuleModal;

                    if (ASG_CONFIG.IS_ADMIN) {

                        initializeInlineEditing();
                        initializeAddLessonFeature();
                    }

                    /**
                     * Generate preview for basic template
                     */
                    function generateBasicPreview() {
                        return `
                            <div class="lesson-template basic-template" style="font-size: 12px; line-height: 1.4;">
                                <div class="lesson-header-basic">
                                    <h1 style="font-size: 18px; color: #0C1B41; font-weight: 700; margin-bottom: 10px;">
                                        [Lesson Title]
                                    </h1>
                                </div>
                                <div class="lesson-content-basic">
                                    <p style="margin-bottom: 8px; color: #666;">
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                                    </p>
                                    <p style="margin-bottom: 8px; color: #666;">
                                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                                    </p>
                                </div>
                            </div>
                        `;
                    }

                    /**
                     * Generate preview for featured image template
                     */
                    function generateFeaturedImagePreview() {
                        return `
                            <div class="lesson-template featured-image-template" style="font-size: 12px; line-height: 1.4;">
                                <h1 style="font-size: 18px; color: #0C1B41; font-weight: 700; margin-bottom: 10px;">
                                    [Lesson Title]
                                </h1>
                                <div style="width: 100%; height: 80px; background: linear-gradient(45deg, #e8f5e8, #c8e6c9); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #2e7d32; font-weight: 600; margin-bottom: 10px;">
                                    🖼️ Featured Image Placeholder
                                </div>
                                <div style="color: #666;">
                                    <p style="margin-bottom: 8px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                                </div>
                            </div>
                        `;
                    }

                    /**
                     * Generate preview for triple layout template
                     */
                    function generateTripleLayoutPreview() {
                        return `
                            <div class="lesson-template triple-layout-template" style="font-size: 10px; line-height: 1.3;">
                                <!-- Hero Section -->
                                <div class="triple-hero-section" style="margin-bottom: 15px;">
                                    <h1 style="font-size: 14px; color: #0C1B41; font-weight: 700; margin-bottom: 8px;">
                                        [Lesson Title]
                                    </h1>
                                    <div class="hero-image-container" style="margin-bottom: 8px;">
                                        <div style="width: 100%; height: 60px; background: linear-gradient(45deg, #e3f2fd, #bbdefb); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #1976d2; font-weight: 600;">
                                            🌟 Hero Image Placeholder
                                        </div>
                                    </div>
                                    <div style="color: #666; font-size: 10px;">
                                        [Hero Content] Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                                    </div>
                                </div>

                                <!-- Left Section -->
                                <div class="triple-left-section" style="margin-bottom: 15px;">
                                    <div class="section-container" style="display: flex; gap: 10px; align-items: flex-start;">
                                        <div class="section-image-left" style="flex: 0 0 80px;">
                                            <div style="width: 80px; height: 50px; background: linear-gradient(45deg, #f3e5f5, #e1bee7); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #7b1fa2; font-weight: 600; font-size: 8px;">
                                                📷 Left
                                            </div>
                                        </div>
                                        <div class="section-content-right" style="flex: 1; color: #666; font-size: 10px;">
                                            <strong>[Left Content]</strong><br>
                                            Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
                                        </div>
                                    </div>
                                </div>

                                <!-- Right Section -->
                                <div class="triple-right-section">
                                    <div class="section-container" style="display: flex; gap: 10px; align-items: flex-start;">
                                        <div class="section-content-left" style="flex: 1; color: #666; font-size: 10px;">
                                            <strong>[Right Content]</strong><br>
                                            Duis aute irure dolor in reprehenderit in voluptate velit esse cillum.
                                        </div>
                                        <div class="section-image-right" style="flex: 0 0 80px;">
                                            <div style="width: 80px; height: 50px; background: linear-gradient(45deg, #fff3e0, #ffcc02); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #f57c00; font-weight: 600; font-size: 8px;">
                                                📷 Right
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    // Simple preview functions for other templates
                    function generateTwoColumnsPreview() {
                        return `<div style="display: flex; gap: 10px; font-size: 12px;">
                            <div style="width: 100px; height: 60px; background: #e3f2fd; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #1976d2;">📊 Image</div>
                            <div style="flex: 1; color: #666;"><strong>[Title]</strong><br>Two columns content...</div>
                        </div>`;
                    }

                    function generateHeroImagePreview() {
                        return `<div style="font-size: 12px;">
                            <div style="width: 100%; height: 80px; background: linear-gradient(45deg, #fff3e0, #ffcc02); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #f57c00; margin-bottom: 10px;">🌟 Hero Image</div>
                            <h1 style="font-size: 18px; color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.5); margin-bottom: 10px;">[Title]</h1>
                            <div style="color: #666;">Hero content below...</div>
                        </div>`;
                    }

                    function generateMediumRightPreview() {
                        return `<div style="display: flex; gap: 10px; font-size: 12px;">
                            <div style="width: 80px; height: 50px; background: #f3e5f5; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #7b1fa2;">◀️ Img</div>
                            <div style="flex: 1; color: #666;"><strong>[Title]</strong><br>Medium right content...</div>
                        </div>`;
                    }

                    function generateMediumLeftPreview() {
                        return `<div style="display: flex; gap: 10px; font-size: 12px;">
                            <div style="flex: 1; color: #666;"><strong>[Title]</strong><br>Medium left content...</div>
                            <div style="width: 80px; height: 50px; background: #fce4ec; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #c2185b;">▶️ Img</div>
                        </div>`;
                    }
                    /**
                     * Initialize add lesson feature
                     */
                    function initializeAddLessonFeature() {
                        console.log('📝 Initializing add lesson feature');

                        // Setup form submission handlers
                        const lessonForm = document.getElementById('addLessonForm');
                        if (lessonForm) {
                            lessonForm.addEventListener('submit', handleAddLessonSubmit);
                        }

                        const moduleForm = document.getElementById('addModuleForm');
                        if (moduleForm) {
                            moduleForm.addEventListener('submit', handleAddModuleSubmit);
                        }

                        // Setup modal close on background click
                        const lessonModal = document.getElementById('addLessonModal');
                        if (lessonModal) {
                            lessonModal.addEventListener('click', (e) => {
                                if (e.target === lessonModal) {
                                    closeAddLessonModal();
                                }
                            });
                        }

                        const moduleModal = document.getElementById('addModuleModal');
                        if (moduleModal) {
                            moduleModal.addEventListener('click', (e) => {
                                if (e.target === moduleModal) {
                                    closeAddModuleModal();
                                }
                            });
                        }

                        // Setup ESC key to close modals
                        document.addEventListener('keydown', (e) => {
                            if (e.key === 'Escape') {
                                const lessonModal = document.getElementById('addLessonModal');
                                const moduleModal = document.getElementById('addModuleModal');

                                if (lessonModal && lessonModal.style.display === 'flex') {
                                    closeAddLessonModal();
                                } else if (moduleModal && moduleModal.style.display === 'flex') {
                                    closeAddModuleModal();
                                }
                            }
                        });
                    }

                    // Exercise functions are now global - see bottom of file

                })(); // Cerrar función anónima - Sistema de Verificación Robusta ASG

                // ===== GLOBAL EXERCISE FUNCTIONS (Outside anonymous function) =====

                /**
                 * Get current lesson ID from URL
                 */
                window.getCurrentLessonId = function() {
                    const urlParams = new URLSearchParams(window.location.search);
                    return urlParams.get('lesson');
                };

                /**
                 * Open add exercise modal
                 */
                window.openAddExerciseModal = function() {
                    const modal = document.getElementById('addExerciseModal');
                    if (modal) {
                        modal.style.display = 'flex';
                        document.body.style.overflow = 'hidden';

                        // Reset form
                        document.getElementById('addExerciseForm').reset();
                        document.getElementById('checklistFields').style.display = 'none';
                        document.getElementById('textfieldFields').style.display = 'none';

                        // Reset checklist items to just one
                        const checklistItems = document.getElementById('checklistItems');
                        checklistItems.innerHTML = `
                            <div class="checklist-item-input">
                                <input type="text" name="checklistItem[]" placeholder="Enter checklist item..." required>
                                <button type="button" class="remove-item-btn" onclick="removeChecklistItem(this)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        `;
                    }
                };

                /**
                 * Close add exercise modal
                 */
                window.closeAddExerciseModal = function() {
                    const modal = document.getElementById('addExerciseModal');
                    if (modal) {
                        modal.style.display = 'none';
                        document.body.style.overflow = 'auto';

                        // Reset form
                        const form = document.getElementById('addExerciseForm');
                        if (form) {
                            form.reset();

                            // Reset form handler to create mode
                            form.onsubmit = handleAddExercise;

                            // Reset submit button text
                            const submitBtn = form.querySelector('button[type="submit"]');
                            if (submitBtn) {
                                submitBtn.innerHTML = '<i class="bi bi-plus-circle"></i> Create Exercise';
                            }

                            // Reset modal title
                            const modalTitle = modal.querySelector('h3');
                            if (modalTitle) {
                                modalTitle.innerHTML = '<i class="bi bi-clipboard-plus"></i> Add Exercise to Lesson';
                            }

                            // Hide all type-specific fields
                            const checklistFields = document.getElementById('checklistFields');
                            const textfieldFields = document.getElementById('textfieldFields');
                            if (checklistFields) checklistFields.style.display = 'none';
                            if (textfieldFields) textfieldFields.style.display = 'none';

                            // Reset checklist items to default
                            const checklistItems = document.getElementById('checklistItems');
                            if (checklistItems) {
                                checklistItems.innerHTML = `
                                    <div class="checklist-item-input">
                                        <input type="text" name="checklistItem[]" placeholder="Enter checklist item...">
                                        <button type="button" class="remove-item-btn" onclick="removeChecklistItem(this)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                `;
                            }
                        }
                    }
                };

                /**
                 * Handle exercise type change
                 */
                window.handleExerciseTypeChange = function() {
                    const exerciseType = document.getElementById('exerciseType').value;
                    const checklistFields = document.getElementById('checklistFields');
                    const textfieldFields = document.getElementById('textfieldFields');

                    // Hide all fields first and disable required validation
                    checklistFields.style.display = 'none';
                    textfieldFields.style.display = 'none';

                    // Remove required attribute from all hidden fields
                    const checklistInputs = checklistFields.querySelectorAll('input[required]');
                    const textfieldInputs = textfieldFields.querySelectorAll('input[required]');

                    checklistInputs.forEach(input => input.removeAttribute('required'));
                    textfieldInputs.forEach(input => input.removeAttribute('required'));

                    // Show relevant fields and add required attributes
                    if (exerciseType === 'checklist') {
                        checklistFields.style.display = 'block';
                        // Add required attribute to visible checklist inputs
                        const visibleChecklistInputs = checklistFields.querySelectorAll('input[name="checklistItem[]"]');
                        visibleChecklistInputs.forEach(input => input.setAttribute('required', 'required'));
                    } else if (exerciseType === 'textfield') {
                        textfieldFields.style.display = 'block';
                        // Add required attribute to visible textfield inputs
                        const visibleTextfieldInputs = textfieldFields.querySelectorAll('input[required]');
                        visibleTextfieldInputs.forEach(input => input.setAttribute('required', 'required'));
                    }
                };

                /**
                 * Add checklist item
                 */
                window.addChecklistItem = function() {
                    const checklistItems = document.getElementById('checklistItems');
                    const exerciseType = document.getElementById('exerciseType').value;
                    const newItem = document.createElement('div');
                    newItem.className = 'checklist-item-input';

                    // Only add required attribute if checklist type is selected
                    const requiredAttr = exerciseType === 'checklist' ? 'required' : '';

                    newItem.innerHTML = `
                        <input type="text" name="checklistItem[]" placeholder="Enter checklist item..." ${requiredAttr}>
                        <button type="button" class="remove-item-btn" onclick="removeChecklistItem(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    `;
                    checklistItems.appendChild(newItem);
                };

                /**
                 * Remove checklist item
                 */
                window.removeChecklistItem = function(button) {
                    const checklistItems = document.getElementById('checklistItems');
                    const items = checklistItems.querySelectorAll('.checklist-item-input');

                    // Don't allow removing if only one item left
                    if (items.length > 1) {
                        button.closest('.checklist-item-input').remove();
                    } else {
                        // Use the global showNotification if available
                        if (window.showNotification) {
                            window.showNotification('At least one checklist item is required', 'warning');
                        } else {
                            alert('At least one checklist item is required');
                        }
                    }
                };

                /**
                 * Handle add exercise form submission
                 */
                window.handleAddExercise = async function(event) {
                    event.preventDefault();

                    const form = event.target;
                    const formData = new FormData(form);
                    const submitBtn = form.querySelector('button[type="submit"]');

                    // Show loading state
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating...';
                    submitBtn.disabled = true;

                    try {
                        // Get current lesson ID
                        const lessonId = window.asgState?.currentLesson?.id || getCurrentLessonId();
                        if (!lessonId) {
                            throw new Error('No lesson ID found');
                        }

                        // Collect form data
                        const exerciseData = {
                            type: formData.get('exerciseType'),
                            title: formData.get('exerciseTitle'),
                            description: formData.get('exerciseDescription') || ''
                        };

                        // Add type-specific data
                        if (exerciseData.type === 'checklist') {
                            const checklistItems = formData.getAll('checklistItem[]').filter(item => item.trim());
                            exerciseData.items = checklistItems;
                        } else if (exerciseData.type === 'textfield') {
                            exerciseData.placeholder = formData.get('textfieldPlaceholder') || 'Write your answer here...';
                            exerciseData.min_words = parseInt(formData.get('minWords')) || 10;
                        }

                        console.log('🎯 Creating exercise via API:', exerciseData);

                        // Send to API
                        const response = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/exercises`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(exerciseData)
                        });

                        const result = await response.json();

                        if (!result.success) {
                            throw new Error(result.error || 'Failed to create exercise');
                        }

                        console.log('✅ Exercise created successfully:', result.data);

                        // Update local state
                        if (window.asgState && window.asgState.currentLesson) {
                            if (!window.asgState.currentLesson.exercises) {
                                window.asgState.currentLesson.exercises = [];
                            }
                            window.asgState.currentLesson.exercises.push(result.data);
                        }

                        // Re-render exercises
                        if (window.renderLessonExercises) {
                            window.renderLessonExercises();
                        }

                        // Close modal
                        closeAddExerciseModal();

                        // Show success message
                        if (window.showNotification) {
                            window.showNotification('Exercise created successfully!', 'success');
                        } else {
                            alert('Exercise created successfully!');
                        }

                    } catch (error) {
                        console.error('❌ Error creating exercise:', error);
                        if (window.showNotification) {
                            window.showNotification('Failed to create exercise: ' + error.message, 'error');
                        } else {
                            alert('Failed to create exercise: ' + error.message);
                        }
                    } finally {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                };

                /**
                 * Delete exercise
                 */
                window.deleteExercise = async function(exerciseId) {
                    if (!confirm('Are you sure you want to delete this exercise?')) return;

                    try {
                        // Get current lesson ID
                        const lessonId = window.asgState?.currentLesson?.id || getCurrentLessonId();
                        if (!lessonId) {
                            throw new Error('No lesson ID found');
                        }

                        console.log('🗑️ Deleting exercise via API:', exerciseId);

                        // Send delete request to API
                        const response = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/exercises/${exerciseId}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        const result = await response.json();

                        if (!result.success) {
                            throw new Error(result.error || 'Failed to delete exercise');
                        }

                        console.log('✅ Exercise deleted successfully');

                        // Update local state
                        if (window.asgState && window.asgState.currentLesson && window.asgState.currentLesson.exercises) {
                            window.asgState.currentLesson.exercises = window.asgState.currentLesson.exercises.filter(
                                exercise => exercise.id !== exerciseId
                            );
                        }

                        // Re-render exercises
                        if (window.renderLessonExercises) {
                            window.renderLessonExercises();
                        }

                        if (window.showNotification) {
                            window.showNotification('Exercise deleted successfully', 'success');
                        } else {
                            alert('Exercise deleted successfully');
                        }

                    } catch (error) {
                        console.error('❌ Error deleting exercise:', error);
                        if (window.showNotification) {
                            window.showNotification('Failed to delete exercise: ' + error.message, 'error');
                        } else {
                            alert('Failed to delete exercise: ' + error.message);
                        }
                    }
                };

                /**
                 * Edit exercise
                 */
                window.editExercise = function(exerciseId) {
                    console.log('✏️ Editing exercise:', exerciseId);

                    // Find the exercise in current lesson
                    const exercise = window.asgState?.currentLesson?.exercises?.find(ex => ex.id == exerciseId);
                    if (!exercise) {
                        console.error('❌ Exercise not found:', exerciseId);
                        if (window.showNotification) {
                            window.showNotification('Exercise not found', 'error');
                        }
                        return;
                    }

                    // Open modal with exercise data
                    const modal = document.getElementById('addExerciseModal');
                    if (!modal) {
                        console.error('❌ Exercise modal not found');
                        return;
                    }

                    // Show modal
                    modal.style.display = 'flex';
                    document.body.style.overflow = 'hidden';

                    // Update modal title
                    const modalTitle = modal.querySelector('h3');
                    if (modalTitle) {
                        modalTitle.textContent = 'Edit Exercise';
                    }

                    // Fill form with exercise data
                    document.getElementById('exerciseType').value = exercise.type;
                    document.getElementById('exerciseTitle').value = exercise.title;
                    document.getElementById('exerciseDescription').value = exercise.description || '';

                    // Handle type-specific fields
                    handleExerciseTypeChange();

                    if (exercise.type === 'checklist' && exercise.content?.items) {
                        const checklistItems = document.getElementById('checklistItems');
                        checklistItems.innerHTML = '';

                        exercise.content.items.forEach((item, index) => {
                            const itemDiv = document.createElement('div');
                            itemDiv.className = 'checklist-item-input';
                            itemDiv.innerHTML = `
                                <input type="text" name="checklistItem[]" value="${item}" required>
                                <button type="button" class="remove-item-btn" onclick="removeChecklistItem(this)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            `;
                            checklistItems.appendChild(itemDiv);
                        });
                    } else if (exercise.type === 'textfield' && exercise.content) {
                        document.getElementById('textfieldPlaceholder').value = exercise.content.placeholder || '';
                        document.getElementById('minWords').value = exercise.content.min_words || 10;
                    }

                    // Change form handler to update instead of create
                    const form = document.getElementById('addExerciseForm');
                    form.onsubmit = function(event) {
                        handleUpdateExercise(event, exerciseId);
                    };

                    // Update submit button text
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.textContent = 'Update Exercise';
                    }
                };

                /**
                 * Handle exercise update
                 */
                window.handleUpdateExercise = async function(event, exerciseId) {
                    event.preventDefault();

                    const form = event.target;
                    const formData = new FormData(form);
                    const submitBtn = form.querySelector('button[type="submit"]');
                    let originalText = submitBtn.innerHTML;

                    try {
                        // Show loading state
                        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Updating...';
                        submitBtn.disabled = true;

                        // Get current lesson ID
                        const lessonId = window.asgState?.currentLesson?.id || getCurrentLessonId();
                        if (!lessonId) {
                            throw new Error('No lesson ID found');
                        }

                        // Collect form data
                        const exerciseData = {
                            type: formData.get('exerciseType'),
                            title: formData.get('exerciseTitle'),
                            description: formData.get('exerciseDescription') || ''
                        };

                        // Add type-specific data
                        if (exerciseData.type === 'checklist') {
                            const checklistItems = formData.getAll('checklistItem[]').filter(item => item.trim());
                            if (checklistItems.length === 0) {
                                throw new Error('At least one checklist item is required');
                            }
                            exerciseData.items = checklistItems;
                        } else if (exerciseData.type === 'textfield') {
                            exerciseData.placeholder = formData.get('textfieldPlaceholder') || 'Write your answer here...';
                            exerciseData.min_words = parseInt(formData.get('minWords')) || 10;
                        }

                        console.log('🎯 Updating exercise via API:', exerciseData);

                        // Send to API
                        const response = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/exercises/${exerciseId}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(exerciseData)
                        });

                        const result = await response.json();

                        if (!result.success) {
                            throw new Error(result.error || 'Failed to update exercise');
                        }

                        console.log('✅ Exercise updated successfully:', result.data);

                        // Update local state
                        if (window.asgState && window.asgState.currentLesson && window.asgState.currentLesson.exercises) {
                            const exerciseIndex = window.asgState.currentLesson.exercises.findIndex(ex => ex.id == exerciseId);
                            if (exerciseIndex !== -1) {
                                window.asgState.currentLesson.exercises[exerciseIndex] = result.data;
                            }
                        }

                        // Close modal
                        closeAddExerciseModal();

                        // Re-render exercises
                        if (window.renderLessonExercises) {
                            await window.renderLessonExercises();
                        }

                        if (window.showNotification) {
                            window.showNotification('Exercise updated successfully!', 'success');
                        } else {
                            alert('Exercise updated successfully!');
                        }

                    } catch (error) {
                        console.error('❌ Error updating exercise:', error);
                        if (window.showNotification) {
                            window.showNotification('Failed to update exercise: ' + error.message, 'error');
                        } else {
                            alert('Failed to update exercise: ' + error.message);
                        }
                    } finally {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;

                        // Reset form handler back to create
                        form.onsubmit = handleAddExercise;

                        // Reset submit button text
                        submitBtn.textContent = 'Create Exercise';

                        // Reset modal title
                        const modalTitle = document.querySelector('#addExerciseModal h3');
                        if (modalTitle) {
                            modalTitle.textContent = 'Add New Exercise';
                        }
                    }
                };

                /**
                 * Render exercises for current lesson
                 */
                window.renderLessonExercises = async function() {
                    console.log('🎨 Rendering lesson exercises...');
                    const exercisesList = document.getElementById('exercisesList');
                    const exercisesContainer = document.getElementById('lessonExercisesContainer');

                    if (!exercisesList) {
                        console.log('❌ Exercises list element not found');
                        return;
                    }

                    if (!window.asgState || !window.asgState.currentLesson) {
                        console.log('❌ No current lesson in state');
                        return;
                    }

                    try {
                        const lessonId = window.asgState.currentLesson.id;
                        console.log('🔍 Loading exercises for lesson ID:', lessonId);

                        // Show loading state
                        exercisesList.innerHTML = `
                            <div class="exercises-loading">
                                <i class="bi bi-hourglass-split"></i>
                                <p>Loading exercises...</p>
                            </div>
                        `;

                        // Load exercises from API
                        const apiUrl = `${ASG_CONFIG.API_BASE}/lessons/${lessonId}/exercises`;
                        console.log('📡 Fetching from:', apiUrl);

                        const response = await fetch(apiUrl);
                        console.log('📥 Response status:', response.status);

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const result = await response.json();
                        console.log('📋 API Result:', result);

                        if (!result.success) {
                            throw new Error(result.error || 'Failed to load exercises');
                        }

                        const exercises = result.data || [];
                        console.log('📋 Loaded exercises from API:', exercises.length);

                        // Debug: Log each exercise
                        exercises.forEach((exercise, index) => {
                            console.log(`Exercise ${index + 1}:`, {
                                id: exercise.id,
                                title: exercise.title,
                                type: exercise.type,
                                content: exercise.content,
                                contentType: typeof exercise.content
                            });
                        });

                        // Update local state
                        window.asgState.currentLesson.exercises = exercises;

                        // Show exercises container for admin or if exercises exist
                        const isAdmin = window.ASG_CONFIG?.IS_ADMIN;
                        if (isAdmin || exercises.length > 0) {
                            exercisesContainer.style.display = 'block';
                            console.log('✅ Showing exercises container');
                        } else {
                            exercisesContainer.style.display = 'none';
                            console.log('❌ Hiding exercises container - no exercises and not admin');
                        }

                        if (exercises.length === 0) {
                            exercisesList.innerHTML = `
                                <div class="exercises-empty">
                                    <i class="bi bi-clipboard"></i>
                                    <p>${isAdmin ? 'No exercises added yet. Click "Add Exercise" to create one.' : 'No practice exercises available for this lesson yet.'}</p>
                                </div>
                            `;
                            return;
                        }

                        // Load progress for each exercise (for students)
                        if (!isAdmin) {
                            console.log('👤 Loading progress for student...');
                            await loadExerciseProgress(exercises);
                        }

                        // Render exercises with error handling for each one
                        const renderedExercises = [];
                        exercises.forEach((exercise, index) => {
                            try {
                                const rendered = renderSingleExercise(exercise);
                                renderedExercises.push(rendered);
                                console.log(`✅ Rendered exercise ${index + 1}: ${exercise.title}`);
                            } catch (error) {
                                console.error(`❌ Error rendering exercise ${index + 1}:`, error);
                                renderedExercises.push(`
                                    <div class="exercise-item error">
                                        <div class="exercise-header">
                                            <div class="exercise-title">
                                                <i class="bi bi-exclamation-triangle"></i>
                                                Error loading exercise: ${exercise.title || 'Unknown'}
                                            </div>
                                        </div>
                                        <div class="exercise-description">
                                            <small class="text-muted">Error: ${error.message}</small>
                                        </div>
                                    </div>
                                `);
                            }
                        });

                        exercisesList.innerHTML = renderedExercises.join('');
                        console.log('✅ Exercises rendered successfully');

                    } catch (error) {
                        console.error('❌ Error loading exercises:', error);
                        exercisesList.innerHTML = `
                            <div class="exercises-error">
                                <i class="bi bi-exclamation-triangle"></i>
                                <p>Error loading exercises: ${error.message}</p>
                                <button onclick="window.renderLessonExercises()" class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="bi bi-arrow-clockwise"></i> Try Again
                                </button>
                            </div>
                        `;
                    }
                };

                /**
                 * Load exercise progress for current user
                 */
                async function loadExerciseProgress(exercises) {
                    try {
                        const lessonId = window.asgState.currentLesson.id;

                        for (const exercise of exercises) {
                            const response = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/exercises/${exercise.id}/progress`);
                            const result = await response.json();

                            if (result.success && result.data) {
                                exercise.progress = result.data;
                                console.log(`📊 Loaded progress for exercise ${exercise.id}:`, result.data);
                            }
                        }
                    } catch (error) {
                        console.error('❌ Error loading exercise progress:', error);
                    }
                }

                /**
                 * Render a single exercise
                 */
                function renderSingleExercise(exercise) {
                    try {
                        console.log('🎨 Rendering exercise:', exercise.title, 'Type:', exercise.type);

                        const isAdmin = window.ASG_CONFIG?.IS_ADMIN || false;
                        const progress = exercise.progress || null;

                        // Validate exercise data
                        if (!exercise.id || !exercise.title || !exercise.type) {
                            throw new Error('Missing required exercise data (id, title, or type)');
                        }

                        // Ensure content is an object
                        let content = exercise.content;
                        if (typeof content === 'string') {
                            try {
                                content = JSON.parse(content);
                            } catch (e) {
                                console.error('❌ Failed to parse exercise content JSON:', e);
                                content = {};
                            }
                        }

                        if (!content || typeof content !== 'object') {
                            console.warn('⚠️ Invalid content for exercise:', exercise.id);
                            content = {};
                        }

                        let exerciseContent = '';

                        if (exercise.type === 'checklist') {
                            const items = content.items || [];
                            if (!Array.isArray(items) || items.length === 0) {
                                exerciseContent = `
                                    <div class="checklist-exercise">
                                        <p class="text-muted">No checklist items configured.</p>
                                    </div>
                                `;
                            } else {
                                const progressData = progress ? (progress.progress_data || {}) : {};
                                exerciseContent = `
                                    <div class="checklist-exercise">
                                        <div class="checklist-items">
                                            ${items.map((item, index) => {
                                                const isChecked = progressData[index] || false;
                                                return `
                                                    <div class="checklist-item ${isChecked ? 'completed' : ''}">
                                                        <input type="checkbox" id="exercise_${exercise.id}_item_${index}"
                                                               ${isChecked ? 'checked' : ''}
                                                               onchange="saveExerciseProgress('${exercise.id}', ${index}, this.checked)">
                                                        <label for="exercise_${exercise.id}_item_${index}">${item}</label>
                                                    </div>
                                                `;
                                            }).join('')}
                                        </div>
                                    </div>
                                `;
                            }
                        } else if (exercise.type === 'textfield') {
                            const savedText = progress ? (progress.progress_data || '') : '';
                            const placeholder = content.placeholder || 'Write your answer here...';
                            const minWords = content.min_words || 10;

                            exerciseContent = `
                                <div class="textfield-exercise">
                                    <textarea
                                        class="exercise-input"
                                        placeholder="${placeholder}"
                                        data-min-words="${minWords}"
                                        data-exercise-id="${exercise.id}"
                                        oninput="updateWordCount('${exercise.id}', this.value)"
                                        onblur="saveTextExerciseProgress('${exercise.id}', this.value)"
                                        onkeyup="updateWordCount('${exercise.id}', this.value)"
                                    >${savedText}</textarea>
                                    <div class="exercise-word-count" id="wordcount_${exercise.id}">
                                        <small class="text-muted">Minimum ${minWords} words</small>
                                    </div>
                                    <div class="exercise-save-status" id="savestatus_${exercise.id}" style="display: none;">
                                        <small class="text-success">
                                            <i class="bi bi-check-circle"></i> Saved
                                        </small>
                                    </div>
                                </div>
                            `;
                        } else {
                            exerciseContent = `
                                <div class="unknown-exercise">
                                    <p class="text-muted">Unknown exercise type: ${exercise.type}</p>
                                </div>
                            `;
                        }

                        return `
                            <div class="exercise-item" data-exercise-id="${exercise.id}">
                                <div class="exercise-header">
                                    <div>
                                        <div class="exercise-title">
                                            <i class="bi bi-${exercise.type === 'checklist' ? 'check2-square' : 'pencil-square'}"></i>
                                            ${exercise.title}
                                        </div>
                                        <span class="exercise-type-badge">${exercise.type}</span>
                                    </div>
                                    ${isAdmin ? `
                                        <div class="exercise-actions">
                                            <button class="exercise-action-btn" onclick="editExercise('${exercise.id}')" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="exercise-action-btn delete" onclick="deleteExercise('${exercise.id}')" title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    ` : ''}
                                </div>
                                ${exercise.description ? `<div class="exercise-description">${exercise.description}</div>` : ''}
                                ${exerciseContent}
                            </div>
                        `;
                    } catch (error) {
                        console.error('❌ Error rendering exercise:', error);
                        return `
                            <div class="exercise-item error">
                                <div class="exercise-header">
                                    <div class="exercise-title">
                                        <i class="bi bi-exclamation-triangle"></i>
                                        Error: ${exercise.title || 'Unknown Exercise'}
                                    </div>
                                </div>
                                <div class="exercise-description">
                                    <small class="text-danger">Render error: ${error.message}</small>
                                </div>
                            </div>
                        `;
                    }
                }

                /**
                 * Initialize exercises when lesson loads
                 */
                window.initializeExercisesForLesson = function() {
                    console.log('🎯 Initializing exercises for lesson...');
                    console.log('Current lesson:', window.asgState?.currentLesson);
                    console.log('Is admin:', window.ASG_CONFIG?.IS_ADMIN);

                    // Load and render exercises from database
                    if (window.renderLessonExercises) {
                        window.renderLessonExercises();
                    }

                    // Show/hide exercises container based on existing exercises and user role
                    const exercisesContainer = document.getElementById('lessonExercisesContainer');
                    const hasExercises = window.asgState?.currentLesson?.exercises?.length > 0;
                    const isAdmin = window.ASG_CONFIG?.IS_ADMIN;

                    console.log('Has exercises:', hasExercises);
                    console.log('Is admin:', isAdmin);
                    console.log('Exercises container:', exercisesContainer);

                    if (exercisesContainer) {
                        // Always show for admins, show for students only if exercises exist
                        if (isAdmin || hasExercises) {
                            exercisesContainer.style.display = 'block';
                            console.log('✅ Showing exercises container');
                        } else {
                            exercisesContainer.style.display = 'none';
                            console.log('❌ Hiding exercises container - no exercises and not admin');
                        }
                    } else {
                        console.log('❌ Exercises container not found');
                    }
                };

                /**
                 * Save exercise progress for checklist items
                 */
                window.saveExerciseProgress = async function(exerciseId, itemIndex, isChecked) {
                    console.log('💾 Saving exercise progress:', exerciseId, itemIndex, isChecked);

                    try {
                        const lessonId = window.asgState?.currentLesson?.id;
                        if (!lessonId) {
                            throw new Error('No lesson ID found');
                        }

                        // Update visual state immediately
                        const checklistItem = document.querySelector(`#exercise_${exerciseId}_item_${itemIndex}`).closest('.checklist-item');
                        if (checklistItem) {
                            if (isChecked) {
                                checklistItem.classList.add('completed');
                            } else {
                                checklistItem.classList.remove('completed');
                            }
                        }

                        // Get current exercise to build progress data
                        const exercise = window.asgState.currentLesson.exercises.find(ex => ex.id == exerciseId);
                        if (!exercise) {
                            throw new Error('Exercise not found');
                        }

                        // Build progress data for all items
                        const progressData = {};
                        exercise.content.items.forEach((item, index) => {
                            const checkbox = document.querySelector(`#exercise_${exerciseId}_item_${index}`);
                            progressData[index] = checkbox ? checkbox.checked : false;
                        });

                        // Save to API
                        const response = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/exercises/${exerciseId}/progress`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                progress_data: progressData
                            })
                        });

                        const result = await response.json();

                        if (!result.success) {
                            throw new Error(result.error || 'Failed to save progress');
                        }

                        console.log('✅ Progress saved successfully');

                        // Show feedback
                        if (window.showNotification) {
                            window.showNotification(isChecked ? 'Progress saved!' : 'Item unchecked', 'success', 1000);
                        }

                    } catch (error) {
                        console.error('❌ Error saving exercise progress:', error);

                        // Revert visual state on error
                        const checklistItem = document.querySelector(`#exercise_${exerciseId}_item_${itemIndex}`).closest('.checklist-item');
                        if (checklistItem) {
                            if (isChecked) {
                                checklistItem.classList.remove('completed');
                                document.querySelector(`#exercise_${exerciseId}_item_${itemIndex}`).checked = false;
                            } else {
                                checklistItem.classList.add('completed');
                                document.querySelector(`#exercise_${exerciseId}_item_${itemIndex}`).checked = true;
                            }
                        }

                        if (window.showNotification) {
                            window.showNotification('Failed to save progress', 'error');
                        }
                    }
                };

                /**
                 * Update word count for text exercises
                 */
                window.updateWordCount = function(exerciseId, text) {
                    const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
                    const wordCountElement = document.getElementById(`wordcount_${exerciseId}`);

                    if (wordCountElement) {
                        const exercise = window.asgState?.currentLesson?.exercises?.find(ex => ex.id == exerciseId);
                        const minWords = exercise?.content?.min_words || 10;
                        const isComplete = wordCount >= minWords;

                        wordCountElement.innerHTML = `
                            <small class="${isComplete ? 'text-success' : 'text-muted'}">
                                ${wordCount} / ${minWords} words ${isComplete ? '✓' : ''}
                            </small>
                        `;

                        // Update textarea border color based on completion
                        const textarea = document.querySelector(`textarea[data-exercise-id="${exerciseId}"]`);
                        if (textarea) {
                            if (isComplete) {
                                textarea.style.borderColor = '#28a745';
                            } else {
                                textarea.style.borderColor = '#e9ecef';
                            }
                        }
                    }
                };

                /**
                 * Save text exercise progress
                 */
                window.saveTextExerciseProgress = async function(exerciseId, text) {
                    console.log('💾 Saving text exercise progress:', exerciseId, text.length, 'characters');

                    try {
                        const lessonId = window.asgState?.currentLesson?.id;
                        if (!lessonId) {
                            throw new Error('No lesson ID found');
                        }

                        // Show saving status
                        const saveStatusElement = document.getElementById(`savestatus_${exerciseId}`);
                        if (saveStatusElement) {
                            saveStatusElement.style.display = 'block';
                            saveStatusElement.innerHTML = `
                                <small class="text-info">
                                    <i class="bi bi-hourglass-split"></i> Saving...
                                </small>
                            `;
                        }

                        // Update word count
                        updateWordCount(exerciseId, text);

                        // Calculate completion
                        const exercise = window.asgState?.currentLesson?.exercises?.find(ex => ex.id == exerciseId);
                        const minWords = exercise?.content?.min_words || 10;
                        const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
                        const isComplete = wordCount >= minWords;
                        const completionPercentage = Math.min(100, Math.round((wordCount / minWords) * 100));

                        // Debounce API calls - only save after user stops typing for 2 seconds
                        if (window.textExerciseTimeout) {
                            clearTimeout(window.textExerciseTimeout);
                        }

                        window.textExerciseTimeout = setTimeout(async () => {
                            try {
                                // Save to API
                                const response = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/exercises/${exerciseId}/progress`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                        progress_data: text
                                    })
                                });

                                const result = await response.json();

                                if (!result.success) {
                                    throw new Error(result.error || 'Failed to save progress');
                                }

                                console.log('✅ Text progress saved successfully');

                                // Show success status
                                if (saveStatusElement) {
                                    saveStatusElement.innerHTML = `
                                        <small class="text-success">
                                            <i class="bi bi-check-circle"></i> Saved
                                        </small>
                                    `;

                                    // Hide after 3 seconds
                                    setTimeout(() => {
                                        saveStatusElement.style.display = 'none';
                                    }, 3000);
                                }

                            } catch (error) {
                                console.error('❌ Error saving text exercise progress:', error);

                                // Show error status
                                if (saveStatusElement) {
                                    saveStatusElement.innerHTML = `
                                        <small class="text-danger">
                                            <i class="bi bi-exclamation-circle"></i> Error saving
                                        </small>
                                    `;
                                }

                                if (window.showNotification) {
                                    window.showNotification('Failed to save progress', 'error');
                                }
                            }
                        }, 2000); // 2 second delay

                    } catch (error) {
                        console.error('❌ Error in saveTextExerciseProgress:', error);
                    }
                };



            </script>
        <?php endif; ?>
    </body>

    </html>
<?php
}
/**
 * Shortcode para renderizar la página de lecciones
 * Uso: [asg_lessons]
 */
function asg_lessons_shortcode($atts)
{
    // Capturar la salida en buffer
    ob_start();

    // Renderizar la página
    asg_render_lessons_page();

    // Retornar el contenido capturado
    return ob_get_clean();
}

// Registrar el shortcode
add_shortcode('asg_lessons', 'asg_lessons_shortcode');

function asg_display_lessons()
{
    echo do_shortcode('[asg_lessons]');
}
