// Restringir acceso a Administradores y Editores
if ( ! is_user_logged_in() || ( ! current_user_can('administrator') && ! current_user_can('editor') ) ) {
    wp_die('You do not have permission to access this page.');
}

// Verificar si el usuario está logueado
$is_user_logged_in = is_user_logged_in();
$user_has_courses = false;
$my_courses_data = [];

// Si el usuario está logueado, obtener los cursos inscritos
if ( $is_user_logged_in ) {
    $request  = new WP_REST_Request('GET', '/asg/v1/my-courses');
    $response = rest_do_request($request);
    if ( ! $response->is_error() ) {
        $data = $response->get_data();
        if ( ! empty( $data['data']['courses'] ) ) {
            $user_has_courses = true;
            $my_courses_data = $data['data']['courses'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Course Management - Ability Seminars Group</title>

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&display=swap" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: 'Outfit', sans-serif; background: #f4f6fa; color: #1a202c; }
    .container { max-width: 80%; margin: 40px auto; padding: 0 20px; }
    h1 { font-size: 2rem; font-weight: 700; margin-bottom: 20px; color: #0C1B40; }

    .controls { margin-bottom: 40px; }
    .category-filters {
      display: flex;
      align-items: center;
      gap: 8px;
      background: linear-gradient(135deg, #0C1B41 0%, #2E5F8A 100%);
      border-radius: 50px;
      padding: 6px 12px;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
    }
    .filter-btn {
      flex: 0 0 auto;
      padding: 8px 16px;
      border: none;
      background: transparent;
      color: rgba(255,255,255,0.9);
      border-radius: 30px;
      cursor: pointer;
      font-weight: 600;
      transition: background 0.3s, color 0.3s;
      white-space: nowrap;
      font-size: 0.9rem;
    }
    .filter-btn.active,
    .filter-btn:hover {
      background: #fff;
      color: #0C1B40;
    }
    .search-input {
      flex: 1 1 auto;
      min-width: 150px;
      padding: 4px 12px;
      border: none;
      border-radius: 28px !important;
      font-size: 0.9rem;
    }
    .search-input:focus { outline: none; }

    .courses-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px,1fr)); gap: 20px; }
    .course-card {
      background: #fff;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      height: 100%;
	  max-width: 480px;
      overflow: hidden;
      transition: transform 0.3s, box-shadow 0.3s;
    }
    .course-card:hover {
      transform: translateY(-6px) scale(1.02);
      box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    }
    .course-image img { width: 100%; height: auto; object-fit: cover; }
    .course-content { padding: 20px; display: flex; flex-direction: column; flex: 1; }
    .course-title { font-size: 1.25rem; font-weight:700; color: #0C1B40; margin-bottom: 10px; }
    .course-description { font-size:0.9rem; color:#4a5568; margin-bottom:15px; flex:1; }
    .course-actions { display:flex; justify-content:center; }

    .btn-action {
      display: block;
      width: 100%;
      padding: 12px;
      background: #0C1B40;
      color: #fff !important;
      text-decoration: none !important;
      font-weight:600;
      text-align:center;
      border-radius: 4px;
      transition: background 0.2s;
    }
    .btn-action:hover { background: #122962; }

    .loading { text-align:center; padding:60px 0; color:#a0aec0; }
    .loading i { font-size:2rem; animation:spin 1s linear infinite; }
    @keyframes spin { to{transform:rotate(360deg);} }

    @media (max-width: 768px) {
      .category-filters { flex-wrap: wrap; white-space: normal; justify-content: center; }
      .filter-btn { flex: 1 1 45%; margin: 4px 0; text-align: center; }
      .search-input { flex: 1 1 100%; margin: 4px 0; }
      body { padding: 20px; }
      h1 { font-size: 1.5rem; }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Course Management</h1>
    <div class="controls">
      <div class="category-filters">
        <button class="filter-btn active" data-category="all">All Categories</button>
        <button class="filter-btn" data-category="finance">Finance</button>
        <button class="filter-btn" data-category="marketing">Marketing</button>
        <button class="filter-btn" data-category="personal-development">Personal Development</button>
        <button class="filter-btn" data-category="technology">Technology</button>
        <button class="filter-btn" data-category="business">Business</button>
        <input type="text" id="searchInput" class="search-input" placeholder="Search courses...">
      </div>
    </div>
    <div id="coursesGrid" class="courses-grid"></div>
    <div id="loading" class="loading" style="display:none;"><i class="fas fa-spinner"></i><p>Loading courses...</p></div>
  </div>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    let coursesList = [];

    function applyFilters() {
      const category = $('.filter-btn.active').data('category');
      const term     = $('#searchInput').val().toLowerCase();
      let filtered   = coursesList.slice();
      if (category !== 'all') filtered = filtered.filter(c => c.category_course === category);
      if (term)          filtered = filtered.filter(c => c.name_course.toLowerCase().includes(term));
      renderGrid(filtered);
    }

    function renderGrid(list) {
      const container = $('#coursesGrid');
      container.empty();
      if (!list.length) return container.html('<p>No courses found.</p>');
      list.forEach(course => {
        const card = $(
          `<div class="course-card">
            <div class="course-image"><img src="${course.cover_img||'https://via.placeholder.com/300x180'}" alt="${course.name_course}"></div>
            <div class="course-content">
              <div class="course-title">${course.name_course}</div>
              <div class="course-description">${course.description_course}</div>
              <div class="course-actions" data-code="${course.code_course}"></div>
            </div>
          </div>`
        );
        container.append(card);
      });
      enhanceButtons();
    }

    // Reemplaza cada placeholder de .course-actions con el botón y URL dinámica
    function enhanceButtons() {
      $('.course-actions').each(function() {
        const code = $(this).data('code');
        const btn = $('<a class="btn-action"></a>')
          .text('Edit Course')
          .attr('href', `/lessons/?course=${code}`);
        $(this).html(btn);
      });
    }

    async function loadCourses() {
      $('#loading').show();
      try {
        const resp = await fetch('/wp-json/asg/v1/courses/api',{credentials:'include'});
        const json = await resp.json();
        coursesList = (json.success && Array.isArray(json.data)) ? json.data : [];
        renderGrid(coursesList);
      } catch (e) {
        $('#coursesGrid').html('<p style="color:#e53e3e;">Error loading courses.</p>');
      } finally {
        $('#loading').hide();
      }
    }

    $(document).ready(() => {
      loadCourses();
      $('.category-filters').on('click','.filter-btn',function(){
        $('.filter-btn').removeClass('active');$(this).addClass('active');applyFilters();
      });
      $('#searchInput').on('input', applyFilters);
    });
  </script>
</body>
</html>
