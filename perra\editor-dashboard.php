// Restringir acceso a Administradores y Editores
if ( ! is_user_logged_in() || ( ! current_user_can('administrator') && ! current_user_can('editor') ) ) {
    wp_die('You do not have permission to access this page.');
}

// Verificar si el usuario está logueado
$is_user_logged_in = is_user_logged_in();
$user_has_courses = false;
$my_courses_data = [];

// Si el usuario está logueado, obtener los cursos inscritos
if ( $is_user_logged_in ) {
    $request  = new WP_REST_Request('GET', '/asg/v1/my-courses');
    $response = rest_do_request($request);
    if ( ! $response->is_error() ) {
        $data = $response->get_data();
        if ( ! empty( $data['data']['courses'] ) ) {
            $user_has_courses = true;
            $my_courses_data = $data['data']['courses'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Course Management - Ability Seminars Group</title>

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&display=swap" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: 'Outfit', sans-serif; background: #f4f6fa; color: #1a202c; }
    .container { max-width: 80%; margin: 40px auto; padding: 0 20px; }
    h1 { font-size: 2rem; font-weight: 700; margin-bottom: 20px; color: #0C1B40; }

    .controls { margin-bottom: 40px; }
    .category-filters {
      display: flex;
      align-items: center;
      gap: 8px;
      background: linear-gradient(135deg, #0C1B41 0%, #2E5F8A 100%);
      border-radius: 50px;
      padding: 6px 12px;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
    }
    .filter-btn {
      flex: 0 0 auto;
      padding: 8px 16px;
      border: none;
      background: transparent;
      color: rgba(255,255,255,0.9);
      border-radius: 30px;
      cursor: pointer;
      font-weight: 600;
      transition: background 0.3s, color 0.3s;
      white-space: nowrap;
      font-size: 0.9rem;
    }
    .filter-btn.active,
    .filter-btn:hover {
      background: #fff;
      color: #0C1B40;
    }
    .search-input {
      flex: 1 1 auto;
      min-width: 150px;
      padding: 4px 12px;
      border: none;
      border-radius: 28px !important;
      font-size: 0.9rem;
    }
    .search-input:focus { outline: none; }

    .courses-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px,1fr)); gap: 20px; }
    .course-card {
      background: #fff;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      height: 100%;
	  max-width: 480px;
      overflow: hidden;
      transition: transform 0.3s, box-shadow 0.3s;
    }
    .course-card:hover {
      transform: translateY(-6px) scale(1.02);
      box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    }

    /* Add New Course Card */
    .add-course-card {
      background: #fff;
      border: 2px dashed #2563eb;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      max-width: 480px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      padding: 2rem;
    }
    .add-course-card:hover {
      transform: translateY(-6px) scale(1.02);
      box-shadow: 0 8px 20px rgba(37, 99, 235, 0.2);
      border-color: #1d4ed8;
      background: #f8faff;
    }
    .add-course-icon {
      font-size: 3rem;
      color: #2563eb;
      margin-bottom: 1rem;
    }
    .add-course-title {
      font-size: 1.25rem;
      font-weight: 700;
      color: #0C1B40;
      margin-bottom: 0.5rem;
    }
    .add-course-subtitle {
      font-size: 0.9rem;
      color: #6b7280;
    }
    .course-image img { width: 100%; height: auto; object-fit: cover; }
    .course-content { padding: 20px; display: flex; flex-direction: column; flex: 1; }
    .course-title { font-size: 1.25rem; font-weight:700; color: #0C1B40; margin-bottom: 10px; }
    .course-description { font-size:0.9rem; color:#4a5568; margin-bottom:15px; flex:1; }
    .course-actions { display:flex; justify-content:center; }

    .btn-action {
      display: block;
      width: 100%;
      padding: 12px;
      background: #0C1B40;
      color: #fff !important;
      text-decoration: none !important;
      font-weight:600;
      text-align:center;
      border-radius: 4px;
      transition: background 0.2s;
    }
    .btn-action:hover { background: #122962; }

    .loading { text-align:center; padding:60px 0; color:#a0aec0; }
    .loading i { font-size:2rem; animation:spin 1s linear infinite; }
    @keyframes spin { to{transform:rotate(360deg);} }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .modal-overlay.show {
      display: flex;
    }
    .modal-content {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e5e7eb;
    }
    .modal-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: #0C1B40;
      margin: 0;
    }
    .modal-close {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #6b7280;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .modal-close:hover {
      color: #374151;
    }

    /* Form Styles */
    .form-group {
      margin-bottom: 1.5rem;
    }
    .form-label {
      display: block;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }
    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.2s;
    }
    .form-control:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    .btn-primary {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      width: 100%;
    }
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    }
    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    /* Image Upload Styles */
    .image-upload-area {
      border: 2px dashed #d1d5db;
      border-radius: 8px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s;
      background-color: #f9fafb;
      margin-top: 0.5rem;
    }
    .image-upload-area:hover {
      border-color: #2563eb;
      background-color: #eff6ff;
    }
    .image-upload-area.dragover {
      border-color: #2563eb;
      background-color: #eff6ff;
    }
    .image-preview {
      max-width: 100%;
      max-height: 200px;
      border-radius: 8px;
      margin-top: 1rem;
      display: none;
    }
    .upload-text {
      color: #6b7280;
      margin: 0.5rem 0;
    }
    .upload-icon {
      font-size: 2rem;
      color: #9ca3af;
      margin-bottom: 0.5rem;
    }

    @media (max-width: 768px) {
      .category-filters { flex-wrap: wrap; white-space: normal; justify-content: center; }
      .filter-btn { flex: 1 1 45%; margin: 4px 0; text-align: center; }
      .search-input { flex: 1 1 100%; margin: 4px 0; }
      body { padding: 20px; }
      h1 { font-size: 1.5rem; }
      .modal-content {
        width: 95%;
        padding: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Course Management</h1>
    <div class="controls">
      <div class="category-filters">
        <button class="filter-btn active" data-category="all">All Categories</button>
        <button class="filter-btn" data-category="finance">Finance</button>
        <button class="filter-btn" data-category="marketing">Marketing</button>
        <button class="filter-btn" data-category="personal-development">Personal Development</button>
        <button class="filter-btn" data-category="technology">Technology</button>
        <button class="filter-btn" data-category="business">Business</button>
        <input type="text" id="searchInput" class="search-input" placeholder="Search courses...">
      </div>
    </div>
    <div id="coursesGrid" class="courses-grid"></div>
    <div id="loading" class="loading" style="display:none;"><i class="fas fa-spinner"></i><p>Loading courses...</p></div>
  </div>

  <!-- Add Course Modal -->
  <div id="addCourseModal" class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Create New Course</h2>
        <button class="modal-close" onclick="closeAddCourseModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form id="addCourseForm">
        <div class="form-group">
          <label for="courseName" class="form-label">Course Title *</label>
          <input type="text" id="courseName" class="form-control" placeholder="e.g., Complete Web Development Bootcamp" required>
        </div>

        <div class="form-group">
          <label for="courseDescription" class="form-label">Course Description *</label>
          <textarea id="courseDescription" class="form-control" rows="3" placeholder="Describe what students will learn..." required></textarea>
        </div>

        <div class="form-group">
          <label for="coursePrice" class="form-label">Course Price ($) *</label>
          <input type="number" id="coursePrice" class="form-control" placeholder="99.00" min="0" step="0.01" required>
        </div>

        <div class="form-group">
          <label for="courseCategory" class="form-label">Category *</label>
          <select id="courseCategory" class="form-control" required>
            <option value="">Select a category</option>
            <option value="finance">Finance</option>
            <option value="marketing">Marketing</option>
            <option value="personal-development">Personal Development</option>
            <option value="technology">Technology</option>
            <option value="business">Business</option>
            <option value="health">Health</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">Course Cover Image</label>
          <div class="image-upload-area" id="imageUploadArea">
            <div class="upload-icon">
              <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <p class="upload-text">Click to upload or drag and drop</p>
            <p class="upload-text" style="font-size: 0.875rem;">PNG, JPG up to 3MB. Recommended: 800x600px</p>
            <input type="file" id="courseImage" accept="image/*" style="display: none;">
          </div>
          <img id="imagePreview" class="image-preview">
          <input type="hidden" id="imageRecordId">
        </div>

        <button type="submit" class="btn-primary" id="createCourseBtn">
          <i class="fas fa-plus-circle"></i> Create Course
        </button>
      </form>
    </div>
  </div>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    let coursesList = [];

    function applyFilters() {
      const category = $('.filter-btn.active').data('category');
      const term     = $('#searchInput').val().toLowerCase();
      let filtered   = coursesList.slice();
      if (category !== 'all') filtered = filtered.filter(c => c.category_course === category);
      if (term)          filtered = filtered.filter(c => c.name_course.toLowerCase().includes(term));
      renderGrid(filtered);
    }

    function renderGrid(list) {
      const container = $('#coursesGrid');
      container.empty();

      // Add existing courses first
      list.forEach(course => {
        const card = $(
          `<div class="course-card">
            <div class="course-image"><img src="${course.cover_img||'https://via.placeholder.com/300x180'}" alt="${course.name_course}"></div>
            <div class="course-content">
              <div class="course-title">${course.name_course}</div>
              <div class="course-description">${course.description_course}</div>
              <div class="course-actions" data-code="${course.code_course}"></div>
            </div>
          </div>`
        );
        container.append(card);
      });

      // Add "Add New Course" card at the end
      const addCourseCard = $(
        `<div class="add-course-card" onclick="openAddCourseModal()">
          <div class="add-course-icon">
            <i class="fas fa-plus-circle"></i>
          </div>
          <div class="add-course-title">Add New Course</div>
          <div class="add-course-subtitle">Click to create a new course</div>
        </div>`
      );
      container.append(addCourseCard);

      enhanceButtons();
    }

    // Reemplaza cada placeholder de .course-actions con el botón y URL dinámica
    function enhanceButtons() {
      $('.course-actions').each(function() {
        const code = $(this).data('code');
        const btn = $('<a class="btn-action"></a>')
          .text('Edit Course')
          .attr('href', `/lessons/?course=${code}`);
        $(this).html(btn);
      });
    }

    async function loadCourses() {
      $('#loading').show();
      try {
        const resp = await fetch('/wp-json/asg/v1/courses/api',{credentials:'include'});
        const json = await resp.json();
        coursesList = (json.success && Array.isArray(json.data)) ? json.data : [];
        renderGrid(coursesList);
      } catch (e) {
        $('#coursesGrid').html('<p style="color:#e53e3e;">Error loading courses.</p>');
      } finally {
        $('#loading').hide();
      }
    }

    // Global variables
    let uploadedImageId = null;

    // Modal functions
    function openAddCourseModal() {
      $('#addCourseModal').addClass('show');
      document.body.style.overflow = 'hidden';
      setupImageUpload();
    }

    function closeAddCourseModal() {
      $('#addCourseModal').removeClass('show');
      document.body.style.overflow = 'auto';
      $('#addCourseForm')[0].reset();
      resetImageUpload();
    }

    // Image upload functions
    function setupImageUpload() {
      const uploadArea = document.getElementById('imageUploadArea');
      const fileInput = document.getElementById('courseImage');
      const preview = document.getElementById('imagePreview');

      // Click to upload
      uploadArea.addEventListener('click', () => fileInput.click());

      // File input change
      fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
          handleImageUpload(e.target.files[0]);
        }
      });

      // Drag and drop
      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
      });

      uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
      });

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
          handleImageUpload(files[0]);
        }
      });
    }

    function resetImageUpload() {
      uploadedImageId = null;
      document.getElementById('imageRecordId').value = '';
      document.getElementById('imagePreview').style.display = 'none';
      document.getElementById('imageUploadArea').innerHTML = `
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <p class="upload-text">Click to upload or drag and drop</p>
        <p class="upload-text" style="font-size: 0.875rem;">PNG, JPG up to 3MB. Recommended: 800x600px</p>
      `;
    }

    // Image upload function
    async function handleImageUpload(file) {
      // Validate file
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }

      if (file.size > 3 * 1024 * 1024) { // 3MB
        alert('Image size must be less than 3MB');
        return;
      }

      try {
        // Show loading state
        const uploadArea = document.getElementById('imageUploadArea');
        uploadArea.innerHTML = `
          <div class="upload-icon">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p class="upload-text">Uploading image...</p>
        `;

        // Upload image
        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', 'course_image');

        const response = await fetch('/wp-json/asg/v1/media/api', {
          method: 'POST',
          headers: {
            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
          },
          body: formData
        });

        const result = await response.json();

        if (result.success && result.data) {
          uploadedImageId = result.data.image_id || result.data.id;
          document.getElementById('imageRecordId').value = uploadedImageId;

          // Show preview
          const preview = document.getElementById('imagePreview');
          preview.src = result.data.url || result.data.medium_url;
          preview.style.display = 'block';

          // Update upload area
          uploadArea.innerHTML = `
            <div class="upload-icon">
              <i class="fas fa-check-circle" style="color: #10b981;"></i>
            </div>
            <p class="upload-text" style="color: #10b981;">Image uploaded successfully!</p>
            <p class="upload-text" style="font-size: 0.875rem;">Click to change image</p>
          `;

          console.log('✅ Image uploaded successfully:', uploadedImageId);
        } else {
          throw new Error(result.message || 'Upload failed');
        }
      } catch (error) {
        console.error('Image upload error:', error);
        alert('Error uploading image: ' + error.message);

        // Reset upload area
        resetImageUpload();
      }
    }

    // Create course function
    async function createCourse(courseData) {
      try {
        const response = await fetch('/wp-json/asg/v1/courses/api', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
          },
          body: JSON.stringify(courseData),
          credentials: 'include'
        });

        const result = await response.json();

        if (result.success) {
          closeAddCourseModal();
          // Show success message
          alert('✅ Course created successfully!');
          // Reload courses
          loadCourses();
        } else {
          throw new Error(result.message || 'Failed to create course');
        }
      } catch (error) {
        console.error('Error creating course:', error);
        alert('❌ Error creating course: ' + error.message);
      }
    }

    $(document).ready(() => {
      loadCourses();
      $('.category-filters').on('click','.filter-btn',function(){
        $('.filter-btn').removeClass('active');$(this).addClass('active');applyFilters();
      });
      $('#searchInput').on('input', applyFilters);

      // Handle form submission
      $('#addCourseForm').on('submit', function(e) {
        e.preventDefault();

        // Get image URL from preview
        const imagePreview = document.getElementById('imagePreview');
        const coverImgUrl = imagePreview.style.display !== 'none' ? imagePreview.src : '';

        const courseData = {
          name_course: $('#courseName').val().trim(),
          description_course: $('#courseDescription').val().trim(),
          price_course: parseFloat($('#coursePrice').val()) || 0,
          category_course: $('#courseCategory').val(),
          status_course: 'published',
          language_course: 'en',
          duration_course: 0,
          cover_img: coverImgUrl,
          image_record_id: uploadedImageId
        };

        // Basic validation
        if (!courseData.name_course || !courseData.description_course || !courseData.category_course) {
          alert('Please fill in all required fields');
          return;
        }

        console.log('Creating course with data:', courseData);
        console.log('🔍 DEBUG - uploadedImageId:', uploadedImageId);
        console.log('🔍 DEBUG - coverImgUrl:', coverImgUrl);

        // Disable button and show loading
        const btn = $('#createCourseBtn');
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Creating...');

        createCourse(courseData).finally(() => {
          btn.prop('disabled', false).html('<i class="fas fa-plus-circle"></i> Create Course');
        });
      });

      // Close modal when clicking outside
      $('#addCourseModal').on('click', function(e) {
        if (e.target === this) {
          closeAddCourseModal();
        }
      });
    });
  </script>
</body>
</html>
