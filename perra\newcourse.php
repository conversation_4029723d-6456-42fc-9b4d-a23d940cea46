/**
 * ASG New Course Page - WordPress Code Snippet - FIXED VERSION
 * 
 * Descripción: Crea la página para crear nuevos cursos con upload de imágenes corregido
 * Versión: 2.1.0 - FIXED IMAGE UPLOAD
 * Autor: AbilitySeminarsGroup
 */

// Evitar acceso directo
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Crear página de nuevo curso
 */
function asg_create_new_course_page() {
    // Verificar permisos
    if (!current_user_can('manage_options')) {
        wp_die(__('No tienes permisos para acceder a esta página.'));
    }
    
     // Get base URL
    $site_url = get_site_url();
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Course - ASG</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <script src="https://cdn.tailwindcss.com"></script>
        
        <style>
            .wg-li weglot-lang weglot-language weglot-flags flag-3 wg-es{
                display: none;
            }
            
            /* Enhanced CSS with better image handling */
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: 'Inter', sans-serif; background: #f8f9fa; }
            
            /* Navigation */
            .navbar { 
                background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%); 
                height: 70px; 
                position: fixed; 
                top: 0; 
                left: 0; 
                right: 0; 
                z-index: 1000; 
            }
            .navbar-brand img { width: 130px; height: auto; }
            .navbar-nav .nav-link { 
                color: white !important; 
                font-weight: 500; 
                padding: 0.5rem 1rem; 
                border-radius: 6px; 
            }
            .navbar-nav .nav-link.active { background-color: rgba(255,255,255,0.2); }
            
            /* Main content */
            .main-content { 
                margin-top: 70px; 
                padding: 2rem; 
                min-height: calc(100vh - 70px); 
            }
            
            /* Page header */
            .page-header { 
                background: white; 
                border-radius: 12px; 
                padding: 2rem; 
                margin-bottom: 2rem; 
                box-shadow: 0 2px 10px rgba(0,0,0,0.05); 
            }
            .page-title { 
                font-size: 2rem; 
                font-weight: 700; 
                color: #1e3a5f; 
                margin-bottom: 0.5rem; 
            }
            
            /* Form container */
            .form-container { 
                background: white; 
                border-radius: 12px; 
                padding: 2rem; 
                box-shadow: 0 2px 10px rgba(0,0,0,0.05); 
            }
            
            /* Form elements */
            .form-group { margin-bottom: 1.5rem; }
            .form-label { 
                font-weight: 600; 
                color: #374151; 
                margin-bottom: 0.5rem; 
                display: block; 
            }
            .form-control { 
                border: 2px solid #e5e7eb; 
                border-radius: 8px; 
                padding: 0.75rem 1rem; 
                font-size: 1rem; 
                transition: all 0.2s; 
            }
            .form-control:focus { 
                border-color: #2563eb; 
                box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1); 
            }
            .form-control.error { border-color: #ef4444; }
            
            /* Categories */
            .category-grid { 
                display: grid; 
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
                gap: 1rem; 
                margin-top: 1rem; 
            }
            .category-option { 
                border: 2px solid #e5e7eb; 
                border-radius: 8px; 
                padding: 1rem; 
                cursor: pointer; 
                transition: all 0.2s; 
                text-align: center; 
            }
            .category-option:hover { border-color: #2563eb; }
            .category-option.selected { 
                border-color: #2563eb; 
                background-color: #eff6ff; 
            }
            .category-icon { font-size: 2rem; margin-bottom: 0.5rem; }
            .category-label { font-weight: 600; color: #374151; }
            
            /* Image upload */
            .image-upload-area { 
                border: 2px dashed #d1d5db; 
                border-radius: 8px; 
                padding: 2rem; 
                text-align: center; 
                cursor: pointer; 
                transition: all 0.2s; 
                background-color: #f9fafb; 
            }
            .image-upload-area:hover { border-color: #2563eb; }
            .image-upload-area.dragover { 
                border-color: #2563eb; 
                background-color: #eff6ff; 
            }
            .image-preview { 
                max-width: 100%; 
                max-height: 300px; 
                border-radius: 8px; 
                margin-top: 1rem; 
            }
            
            /* Buttons */
            .btn { 
                padding: 0.75rem 1.5rem; 
                border-radius: 8px; 
                font-weight: 600; 
                text-decoration: none; 
                display: inline-flex; 
                align-items: center; 
                gap: 0.5rem; 
                transition: all 0.2s; 
                border: none; 
                cursor: pointer; 
            }
            .btn-primary { 
                background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); 
                color: white; 
            }
            .btn-primary:hover { transform: translateY(-1px); }
            .btn-secondary { 
                background: #6b7280; 
                color: white; 
            }
            .btn-outline { 
                background: transparent; 
                border: 2px solid #d1d5db; 
                color: #374151; 
            }
            .btn-group { 
                display: flex; 
                gap: 1rem; 
                justify-content: flex-end; 
                margin-top: 2rem; 
            }
            
            /* Loading states */
            .loading { opacity: 0.6; pointer-events: none; }
            .spinner { 
                display: inline-block; 
                width: 1rem; 
                height: 1rem; 
                border: 2px solid transparent; 
                border-top: 2px solid currentColor; 
                border-radius: 50%; 
                animation: spin 1s linear infinite; 
            }
            @keyframes spin { to { transform: rotate(360deg); } }
            
            /* Error messages */
            .error-message { 
                color: #ef4444; 
                font-size: 0.875rem; 
                margin-top: 0.25rem; 
                display: none; 
            }
            .form-text { 
                color: #6b7280; 
                font-size: 0.875rem; 
                margin-top: 0.25rem; 
            }
            
            /* Notifications */
            .notification { 
                position: fixed; 
                top: 90px; 
                right: 20px; 
                padding: 1rem 1.5rem; 
                border-radius: 8px; 
                color: white; 
                font-weight: 600; 
                z-index: 1001; 
                transform: translateX(100%); 
                transition: transform 0.3s; 
            }
            .notification.show { transform: translateX(0); }
            .notification.success { background: #10b981; }
            .notification.error { background: #ef4444; }
            .notification.info { background: #2563eb; }
            
            /* Responsive */
            @media (max-width: 768px) {
                .main-content { padding: 1rem; }
                .page-header { padding: 1.5rem; }
                .form-container { padding: 1.5rem; }
                .btn-group { flex-direction: column; }
                .category-grid { grid-template-columns: 1fr; }
            }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo $site_url; ?>/admin-dashboard/">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo" style="width:130px;height:auto;">
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/admin-dashboard/">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/all-courses/">
                                <i class="bi bi-collection"></i> All Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="<?php echo $site_url; ?>/new-course-optimized/">
                                <i class="bi bi-plus-circle"></i> New Course
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="bi bi-plus-circle text-blue-600"></i>
                    Create New Course
                </h1>
                <p class="text-gray-600">Fill in the information below to create a new course in the system</p>
            </div>

            <!-- Form Container -->
            <div class="form-container">
                <form id="newCourseForm">
                    <!-- Course Title -->
                    <div class="form-group">
                        <label for="courseTitle" class="form-label">
                            Course Title *
                        </label>
                        <input 
                            type="text" 
                            id="courseTitle" 
                            class="form-control" 
                            placeholder="e.g., Complete Web Development Bootcamp"
                            required
                        >
                        <div class="error-message" id="courseTitleError"></div>
                    </div>

                    <!-- Course Description -->
                    <div class="form-group">
                        <label for="courseDescription" class="form-label">
                            Course Description *
                        </label>
                        <textarea 
                            id="courseDescription" 
                            class="form-control" 
                            rows="4" 
                            placeholder="Describe what students will learn in this course..."
                            required
                        ></textarea>
                        <div class="error-message" id="courseDescriptionError"></div>
                    </div>

                    <!-- Course Price and Duration -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="coursePrice" class="form-label">
                                    Course Price ($) *
                                </label>
                                <input 
                                    type="number" 
                                    id="coursePrice" 
                                    class="form-control" 
                                    placeholder="99.00"
                                    min="0" 
                                    step="0.01"
                                    required
                                >
                                <div class="error-message" id="coursePriceError"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="courseDuration" class="form-label">
                                    Duration (hours)
                                </label>
                                <input 
                                    type="number" 
                                    id="courseDuration" 
                                    class="form-control" 
                                    placeholder="10"
                                    min="1"
                                >
                            </div>
                        </div>
                    </div>

                    <!-- Course Language -->
                    <div class="form-group">
                        <label for="courseLanguage" class="form-label">
                            Course Language
                        </label>
                        <select id="courseLanguage" class="form-control">
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="pt">Portuguese</option>
                        </select>
                    </div>

                    <!-- Course Category -->
                    <div class="form-group">
                        <label class="form-label">Course Category *</label>
                        <div class="category-grid" id="categoryGrid">
                            <!-- Categories will be populated by JavaScript -->
                        </div>
                        <input type="hidden" id="selectedCategory" required>
                        <div class="error-message" id="categoryError"></div>
                    </div>

                    <!-- Course Image -->
                    <div class="form-group">
                        <label class="form-label">Course Cover Image</label>
                        <div class="image-upload-area" id="imageUploadArea">
                            <i class="bi bi-cloud-upload text-4xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600 mb-2">Click to upload or drag and drop</p>
                            <p class="text-sm text-gray-500">PNG, JPG up to 3MB. Recommended: 800x600px</p>
                            <input type="file" id="courseImage" accept="image/*" style="display: none;">
                        </div>
                        <img id="imagePreview" class="image-preview" style="display: none;">
                        <input type="hidden" id="imageRecordId">
                        <div class="error-message" id="courseImageError"></div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline" onclick="cancelForm()">
                            <i class="bi bi-x-circle"></i> Cancel
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="saveDraft()">
                            <i class="bi bi-file-earmark"></i> Save Draft
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="bi bi-plus-circle"></i> Create Course
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Scripts -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <!-- JavaScript Optimized -->
        <script>
            // Configuration
            const ASG_CONFIG = {
                version: '1.0.0',
                apiUrl: '<?php echo $site_url; ?>/wp-json/asg/v1',
                siteUrl: '<?php echo $site_url; ?>',
                categories: {
                    finance: { label: 'Finance', icon: '💰' },
                    marketing: { label: 'Marketing', icon: '📱' },
                    'personal-development': { label: 'Personal Development', icon: '🧠' },
                    technology: { label: 'Technology', icon: '💻' },
                    business: { label: 'Business', icon: '💼' },
                    health: { label: 'Health', icon: '🏥' }
                }
            };

            // Optimized API Client
            class ASGApiClientOptimized {
                constructor() {
                    this.baseUrl = ASG_CONFIG.apiUrl;
                }

                async request(endpoint, options = {}) {
                    const url = `${this.baseUrl}${endpoint}`;
                    const defaultOptions = {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
                        }
                    };

                    try {
                        const response = await fetch(url, { ...defaultOptions, ...options });
                        if (!response.ok) throw new Error(`HTTP ${response.status}`);
                        return await response.json();
                    } catch (error) {
                        console.error('API Error:', error);
                        throw error;
                    }
                }

                async uploadImage(imageFile, onProgress = null) {
                    try {
                        const formData = new FormData();
                        formData.append('image', imageFile);
                        formData.append('type', 'course_image');

                        return new Promise((resolve, reject) => {
                            const xhr = new XMLHttpRequest();

                            if (onProgress) {
                                xhr.upload.addEventListener('progress', (e) => {
                                    if (e.lengthComputable) {
                                        const percentComplete = (e.loaded / e.total) * 100;
                                        onProgress(percentComplete);
                                    }
                                });
                            }

                            xhr.addEventListener('load', () => {
                                if (xhr.status === 200) {
                                    try {
                                        const response = JSON.parse(xhr.responseText);
                                        resolve(response);
                                    } catch (e) {
                                        reject(new Error('Invalid JSON response'));
                                    }
                                } else {
                                    reject(new Error(`HTTP ${xhr.status}`));
                                }
                            });

                            xhr.addEventListener('error', () => {
                                reject(new Error('Network error'));
                            });

                            xhr.open('POST', `${this.baseUrl}/media/api`);
                            xhr.setRequestHeader('X-WP-Nonce', '<?php echo wp_create_nonce('wp_rest'); ?>');
                            xhr.send(formData);
                        });
                    } catch (error) {
                        console.error('Upload image error:', error);
                        throw error;
                    }
                }

                async createCourse(courseData) {
                    try {
                        const response = await this.request('/courses/api', {
                            method: 'POST',
                            body: JSON.stringify(courseData)
                        });
                        return response;
                    } catch (error) {
                        console.error('Create course error:', error);
                        throw error;
                    }
                }
            }

            const ASG_API = new ASGApiClientOptimized();

            // Global variables
            let selectedCategory = null;
            let uploadedImageId = null;

            // Utility functions
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    ${message}
                `;

                document.body.appendChild(notification);

                setTimeout(() => notification.classList.add('show'), 100);
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 300);
                }, 4000);
            }

            function setLoading(element, loading = true) {
                if (loading) {
                    element.classList.add('loading');
                    const icon = element.querySelector('i');
                    if (icon) {
                        icon.className = 'spinner';
                    }
                } else {
                    element.classList.remove('loading');
                    const spinner = element.querySelector('.spinner');
                    if (spinner) {
                        spinner.className = 'bi bi-plus-circle';
                    }
                }
            }

            function validateForm() {
                let isValid = true;

                // Clear previous errors
                document.querySelectorAll('.error-message').forEach(el => el.style.display = 'none');
                document.querySelectorAll('.form-control').forEach(el => el.classList.remove('error'));

                // Validate title
                const title = document.getElementById('courseTitle').value.trim();
                if (!title) {
                    showFieldError('courseTitle', 'Course title is required');
                    isValid = false;
                } else if (title.length < 3) {
                    showFieldError('courseTitle', 'Course title must be at least 3 characters');
                    isValid = false;
                }

                // Validate description
                const description = document.getElementById('courseDescription').value.trim();
                if (!description) {
                    showFieldError('courseDescription', 'Course description is required');
                    isValid = false;
                } else if (description.length < 10) {
                    showFieldError('courseDescription', 'Course description must be at least 10 characters');
                    isValid = false;
                }

                // Validate price
                const price = parseFloat(document.getElementById('coursePrice').value);
                if (!price || price <= 0) {
                    showFieldError('coursePrice', 'Please enter a valid price');
                    isValid = false;
                }

                // Validate category
                if (!selectedCategory) {
                    showFieldError('category', 'Please select a course category');
                    isValid = false;
                }

                return isValid;
            }

            function showFieldError(fieldId, message) {
                const field = document.getElementById(fieldId);
                const errorElement = document.getElementById(fieldId + 'Error');

                if (field) field.classList.add('error');
                if (errorElement) {
                    errorElement.textContent = message;
                    errorElement.style.display = 'block';
                }
            }

            // Setup functions
            function setupCategories() {
                const categoryGrid = document.getElementById('categoryGrid');

                Object.entries(ASG_CONFIG.categories).forEach(([key, category]) => {
                    const categoryElement = document.createElement('div');
                    categoryElement.className = 'category-option';
                    categoryElement.dataset.category = key;
                    categoryElement.innerHTML = `
                        <div class="category-icon">${category.icon}</div>
                        <div class="category-label">${category.label}</div>
                    `;

                    categoryElement.addEventListener('click', () => selectCategory(key, categoryElement));
                    categoryGrid.appendChild(categoryElement);
                });
            }

            function selectCategory(categoryKey, element) {
                // Remove previous selection
                document.querySelectorAll('.category-option').forEach(el => el.classList.remove('selected'));

                // Add selection to clicked element
                element.classList.add('selected');
                selectedCategory = categoryKey;
                document.getElementById('selectedCategory').value = categoryKey;

                // Clear error if any
                document.getElementById('categoryError').style.display = 'none';
            }

            function setupImageUpload() {
                const uploadArea = document.getElementById('imageUploadArea');
                const fileInput = document.getElementById('courseImage');
                const preview = document.getElementById('imagePreview');

                // Click to upload
                uploadArea.addEventListener('click', () => fileInput.click());

                // File input change
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        handleImageUpload(e.target.files[0]);
                    }
                });

                // Drag and drop
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleImageUpload(files[0]);
                    }
                });
            }

            async function handleImageUpload(file) {
                // Validate file
                if (!file.type.startsWith('image/')) {
                    showNotification('Please select a valid image file', 'error');
                    return;
                }

                if (file.size > 3 * 1024 * 1024) { // 3MB
                    showNotification('Image size must be less than 3MB', 'error');
                    return;
                }

                try {
                    // Show loading state
                    const uploadArea = document.getElementById('imageUploadArea');
                    uploadArea.innerHTML = `
                        <div class="spinner mx-auto mb-3"></div>
                        <p class="text-gray-600">Uploading image...</p>
                    `;

                    // Upload image
                    const response = await ASG_API.uploadImage(file, (progress) => {
                        uploadArea.innerHTML = `
                            <div class="spinner mx-auto mb-3"></div>
                            <p class="text-gray-600">Uploading... ${Math.round(progress)}%</p>
                        `;
                    });

                    console.log('🔍 Upload response:', response);
                    console.log('🔍 response.data:', response.data);
                    console.log('🔍 response.data.id:', response.data?.id);

                    if (response.success && response.data) {
                        uploadedImageId = response.data.image_id || response.data.id; // ← ARREGLADO
                        document.getElementById('imageRecordId').value = uploadedImageId;

                        console.log('🔍 uploadedImageId set to:', uploadedImageId);

                        // Show preview
                        const preview = document.getElementById('imagePreview');
                        preview.src = response.data.url || response.data.medium_url;
                        preview.style.display = 'block';

                        // Update upload area
                        uploadArea.innerHTML = `
                            <i class="bi bi-check-circle text-4xl text-green-500 mb-3"></i>
                            <p class="text-green-600 mb-2">Image uploaded successfully!</p>
                            <p class="text-sm text-gray-500">Click to change image</p>
                        `;

                        showNotification('Image uploaded successfully!', 'success');
                    } else {
                        throw new Error(response.message || 'Upload failed');
                    }
                } catch (error) {
                    console.error('Image upload error:', error);
                    showNotification('Error uploading image: ' + error.message, 'error');

                    // Reset upload area
                    document.getElementById('imageUploadArea').innerHTML = `
                        <i class="bi bi-cloud-upload text-4xl text-gray-400 mb-3"></i>
                        <p class="text-gray-600 mb-2">Click to upload or drag and drop</p>
                        <p class="text-sm text-gray-500">PNG, JPG up to 3MB. Recommended: 800x600px</p>
                    `;
                }
            }

            // Form submission functions
            async function submitForm(isDraft = false) {
                if (!validateForm() && !isDraft) {
                    showNotification('Please correct the errors in the form', 'error');
                    return;
                }

                const submitBtn = document.getElementById('submitBtn');

                try {
                    setLoading(submitBtn, true);

                    // Get image URL from preview
                    const imagePreview = document.getElementById('imagePreview');
                    const coverImgUrl = imagePreview.style.display !== 'none' ? imagePreview.src : '';

                    // Collect form data
                    const courseData = {
                        name_course: document.getElementById('courseTitle').value.trim(),
                        description_course: document.getElementById('courseDescription').value.trim(),
                        price_course: parseFloat(document.getElementById('coursePrice').value) || 0,
                        duration_course: parseInt(document.getElementById('courseDuration').value) || 0,
                        language_course: document.getElementById('courseLanguage').value,
                        category_course: selectedCategory,
                        status_course: isDraft ? 'draft' : 'published',
                        cover_img: coverImgUrl, // ← URL de la imagen
                        image_record_id: uploadedImageId // ← ID del registro de imagen
                    };

                    console.log('Creating course with data:', courseData);
                    console.log('🔍 DEBUG - uploadedImageId:', uploadedImageId);
                    console.log('🔍 DEBUG - coverImgUrl:', coverImgUrl);
                    console.log('🔍 DEBUG - image_record_id in courseData:', courseData.image_record_id);

                    const result = await ASG_API.createCourse(courseData);

                    if (result.success) {
                        showNotification(`✅ Course ${isDraft ? 'saved as draft' : 'created'} successfully!`, 'success');

                        setTimeout(() => {
                            if (result.data && result.data.code_course) {
                                window.location.href = `${ASG_CONFIG.siteUrl}/edit-course/#${result.data.code_course}`;
                            } else {
                                window.location.href = `${ASG_CONFIG.siteUrl}/all-courses/`;
                            }
                        }, 2000);
                    } else {
                        throw new Error(result.message || 'Unknown error');
                    }

                } catch (error) {
                    console.error('Error creating course:', error);
                    showNotification('Error creating course: ' + error.message, 'error');
                } finally {
                    setLoading(submitBtn, false);
                }
            }

            function saveDraft() {
                submitForm(true);
            }

            function cancelForm() {
                if (confirm('Are you sure you want to cancel? All unsaved changes will be lost.')) {
                    window.location.href = `${ASG_CONFIG.siteUrl}/all-courses/`;
                }
            }

            // Event Listeners
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🚀 New Course Optimized v1.0.0 loaded');

                setupCategories();
                setupImageUpload();

                // Form submission
                document.getElementById('newCourseForm').addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitForm(false);
                });

                // Real-time validation
                ['courseTitle', 'courseDescription', 'coursePrice'].forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        field.addEventListener('input', () => {
                            field.classList.remove('error');
                            document.getElementById(fieldId + 'Error').style.display = 'none';
                        });
                    }
                });

                showNotification('✨ New course form loaded successfully', 'success');
            });
        </script>
    </body>
    </html>
    <?php
}


/**
 * Registrar la página de nuevo curso
 */
function asg_register_new_course_page() {
    $page_slug = 'new-course';
    $page = get_page_by_path($page_slug);

    if (!$page) {
        $page_data = array(
            'post_title'    => 'Nuevo Curso ASG',
            'post_content'  => '[asg_new_course]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug
        );

        wp_insert_post($page_data);
    }
}

/**
 * Shortcode para mostrar el formulario de nuevo curso
 */
function asg_new_course_shortcode($atts) {
    ob_start();
    asg_create_new_course_page();
    return ob_get_clean();
}

// Registrar hooks
add_action('init', 'asg_register_new_course_page');
add_shortcode('asg_new_course', 'asg_new_course_shortcode');

// Registrar ruta personalizada
add_action('init', function() {
    add_rewrite_rule('^new-course/?$', 'index.php?asg_page=new_course', 'top');
});

add_filter('query_vars', function($vars) {
    $vars[] = 'asg_page';
    return $vars;
});

add_action('template_redirect', function() {
    $asg_page = get_query_var('asg_page');
    if ($asg_page === 'new_course') {
        asg_create_new_course_page();
        exit;
    }
});
